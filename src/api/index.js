// import server from './server'
import { server, postRequest, postParamsQS, getRequest, getRequestLinen, postRequestKgce, getRequestKgce, postProject, getProject, postRequestNoToken, post_json, post_formData } from "./http";
import constructionOperation from "./constructionOperation";
console.log("__PATH=========", process);
let ssoApi = process.env.API_HOST;
let baseApi = process.env.API_BASE;
let iomsApi = __PATH.ONESTOP;
let imwsApi = process.env.API_IMWS;
let ipsmApi = __PATH.IPSM_URL;
let ipsmdbApi = __PATH.IPSM_URL2;
let authwebApi = __PATH.AUTH_WEB;
let imwsapi = __PATH.IMWS_API;
let spaceApi = __PATH.BASE_API;
let alarmApi = __PATH.ALARM;
let quesApi = __PATH.WJ_API;
let iemcApi = __PATH.IEMC_API;
let linenApi = __PATH.Linen_API;
let kgceApi = __PATH.KGCE_API;
let sApi = __PATH.SPACE_API;
let newsApi = __PATH.NEWS_API;
let dorm = __PATH.DORM;
let imem = __PATH.IMEM_API;
let Lab = __PATH.LAB_API;
let courseApi = __PATH.LAB_COURSE;
let ipsmsApi = __PATH.BASEURL;

let appRecordForm = __PATH.APP_RECORD_API;
let planApi = __PATH.PLAN_API;
let chemicalWebApi = __PATH.SINOMIS_CHEMICALWEB;
console.log("ssoApi=============", ssoApi);

//选择服务地点
const cascadingQueryHospitalGridInfo = data =>
  server({
    url: `${baseApi}/hospitalController/cascadingQueryHospitalGridInfo`,
    data
  });
//选择服务地点树
const getGridTreeData = data =>
  server({
    url: `${spaceApi}/space/api/getAllSpaceInfo`,
    data
  });
//获取openId
const getOpenid = data => server({ url: `${ssoApi}/userController/getOpenid`, data });
//登录
const userLoginIn = data => server({ url: `${ssoApi}/userController/userLoginIn`, data });
//更新用户信息
const saveUserWechat = data => server({ url: `${ssoApi}/userController/saveUserWechat`, data });
//获取职工信息
const getStaffInfoByType = data => server({ url: `${baseApi}/hospitalStaff/hospital-staff/getDetails`, data });
// const getStaffInfoByType = data => server({ url: `${baseApi}/outsourcedController/getStaffInfoByType`, data })
//注册时选择部门
const getHospitalOfficeInfo = data =>
  server({
    url: `${baseApi}/departmentManager/department-manager/selectByPage`,
    data
  });
// const getHospitalOfficeInfo = data => server({ url: `${baseApi}/hospitalController/getHospitalOfficeInfo`, data })
//注册时选择职务
const getDictArray = data => server({ url: `${baseApi}/dictController/getDictArray`, data });
//主管单位下医院列表（关联基础信息logo）
const getHospitalListAndLogoByUnitCode = data =>
  server({
    url: `${baseApi}/hospitalController/getHospitalListAndLogoByUnitCode`,
    data
  });
//获取医院开放注册状态
const getHospitalIsRisterByCode = data =>
  server({
    url: `${baseApi}/hospitalController/getHospitalIsRisterByCode`,
    data
  });
//用户注册(校验：职工信息(含手机号)是否yerver({ url: `${ssoApi}/userController/userRegisterValidate`, data })
//用户注册
const userRegister = data => server({ url: `${ssoApi}/userController/userRegister`, data });
//级联查询行政区域（医院选择时的所属省份）
const cascadingQueryAreaList = data => server({ url: `${baseApi}/dictController/cascadingQueryAreaList`, data });
//个人中心信息修改
const updateMyCenter = data => server({ url: `${ssoApi}/userController/updateMyCenter`, data });
//组织机构+班组列表(获取班组列表)
const getOfficeAndTeamList = data => server({ url: `${baseApi}/outsourcedController/getOfficeAndTeamList`, data });
// 更新职工信息
const updateStaffInfo = data => server({ url: `${baseApi}/staffController/updateStaffInfo`, data });
//服务事项
const getItemTypeList = data =>
  server({
    url: `${iomsApi}/appOlgTaskManagement.do?getItemTypeListNew`,
    data
  });
//获取个人中心数据
const getPersonalCentre = data =>
  server({
    url: `${iomsApi}/appOlgTaskManagement.do?getPersonalCentre`,
    data,
    ifCode: true
  });
// 获取待办事项工单列表
const getStayTaskOrderList = data => server({ url: `${iomsApi}/iHCRSStatisticsController/getStayTaskOrderList`, data });
//获取个人中心 班组人员我的任务统计数据
const getTeamWorkInfo = data =>
  server({
    url: `${iomsApi}//iHCRSStatisticsController/getTeamWorkInfo`,
    data,
    ifCode: true
  });
//所属科室列表
const getAllOffice = data => server({ url: `${iomsApi}/iHCRSStatisticsController/getAllOffice`, data });
//我的工单（列表）
const getMineTask = data => server({ url: `${iomsApi}/appOlgTaskManagement.do?getMineTask`, data });
//工单任务详情
const getTaskDetail = data => server({ url: `${iomsApi}/appOlgTaskManagement.do?getTaskDetail`, data });
//工单回退
const appRollbackTask = data => server({ url: `${iomsApi}/deviceRepair/appRollbackTask`, data });
//查询未评价工单配置
const getNotCommentWarn = data => postRequest({ url: `${iomsApi}/appDisOlgTask/getNotCommentWarn`, data });
//获取耗材目录列表
const getDepotCatalogueList = data => server({ url: `${iomsApi}/appDisOlgTask.do?getDepotCatalogueList`, data });
//获取耗材明细列表
const getDepotManagementList = data => server({ url: `${iomsApi}/appDisOlgTask.do?getDepotManagementList`, data });
//挂单原因列表
const getEntryOrdersReason = data => server({ url: `${iomsApi}/appDisOlgTask.do?getEntryOrdersReason`, data });
//工单派工、抢单
const saveScheduling = data => server({ url: `${iomsApi}/appDisOlgTask.do?saveScheduling`, data });
//工单挂单
const saveEntryOrders = data => server({ url: `${iomsApi}/appDisOlgTask.do?saveEntryOrders`, data });
//工单完工
const saveComplete = data => server({ url: `${iomsApi}/appDisOlgTask.do?saveComplete`, data });
//发表评价
const toSaveEvaluate = data => server({ url: `${iomsApi}/appOlgTaskManagement.do?toSaveEvaluate`, data });
//取消工单原因列表
const getCancelReason = data => server({ url: `${iomsApi}/appOlgTaskManagement.do?getCancelReason`, data });
//查询最近三次地点(服务区域中)
const getLastThreeLocations = data =>
  server({
    url: `${iomsApi}/appOlgTaskManagement.do?getLastThreeLocations`,
    data
  });
//获取医院自定义的字典项接口
const getPersonnelDictionary = data =>
  server({
    url: `${iomsApi}/appOlgTaskManagement.do?getPersonnelDictionary`,
    data
  });
//获取医院自定义的字典项接口
// const getTeamsByTask = data => server({ url: `${iomsApi}/appOlgTaskManagement.do?getTeamsByTask`, data })
const getTeamsByTask = data => server({ url: `${iomsApi}//iHCRSStatisticsController/getOCTeamInfo`, data });
const getTeamsByTaskAll = data => server({ url: `${iomsApi}/iHCRSStatisticsController/geAllTOCTeamInfo`, data });
//工单转单获取指派班组列表
const toTeamsChangeTask = data => server({ url: `${iomsApi}/appOlgTaskManagement.do?toTeamsChangeTask`, data });
const toTeamsChangeTaskNew = data => server({ url: `${iomsApi}/appDisOlgTask.do?toTeamsChangeTask`, data });
//获取指派人员列表
// const getDesignatePersonList = data => server({ url: `${iomsApi}/appDisOlgTask.do?getDesignatePersonList`, data })
const getDesignatePersonList = data =>
  server({
    url: `${iomsApi}/iHCRSStatisticsController/getOCTeamMemberInfo`,
    data
  });
//工单转单
const getHospitalDispatchingConfig = data =>
  server({
    url: `${iomsApi}/appOlgTaskManagement.do?getHospitalDispatchingConfig`,
    data
  });

//获取故障原因/维修方法列表
const getMalfunctionReasonMethod = data =>
  server({
    url: `${iomsApi}/appDisOlgTask.do?getMalfunctionReasonMethod`,
    data
  });
// 新增故障原因/维修方法
const saveMalfunctionReasonMethod = data =>
  server({
    url: `${iomsApi}/appDisOlgTask.do?saveMalfunctionReasonMethod`,
    data
  });

const updateTask = data => server({ url: `${iomsApi}/appDisOlgTask.do?updateTask`, data }); //工单修改
const getTransportType = data => server({ url: `${iomsApi}/appDisOlgTask.do?getTransportType`, data }); //工勤搬运和综合服务服务事项获取

const getOfficeWasteInfo = data => server({ url: `${imwsApi}/appInterfaceController/getOfficeWasteInfo`, data }); //获取该科室本次医废收集信息
const wasteRecordMonthlyReport = data =>
  server({
    url: `${imwsApi}/appInterfaceController/wasteRecordMonthlyReport`,
    data
  }); //获取医废月报信息
const wasteRecordMonthlyReportDetail = data =>
  server({
    url: `${imwsApi}/appInterfaceController/wasteRecordMonthlyReportDetail`,
    data
  }); //医废月报日点击详情

const findWeightRecordListBySign = data =>
  server({
    url: `${imwsApi}/statisticsInterfaceController/findWeightRecordListBySign`,
    data
  }); //医废列表
const officeSignature = data => server({ url: `${imwsApi}/appInterfaceController/officeSignature`, data }); //医废签名

//我的工作列表
const getMineTaskPage = data => server({ url: `${iomsApi}/appOlgTaskManagement.do?getMineTaskPage`, data });

//意见箱相关 接口

//获取意见反馈列表
const getOpinionList = data => server({ url: `${iomsApi}/appDisOpinionController.do?getOpinionList`, data });
//获取意见反馈详情
const getOpinionDetail = data =>
  server({
    url: `${iomsApi}/appDisOpinionController.do?getOpinionDetail`,
    data
  });
//获取意见反馈列表
const replyOpinion = data => server({ url: `${iomsApi}/appDisOpinionController.do?replyOpinion`, data });
//新建意见反馈
const saveOpinion = data => server({ url: `${iomsApi}/appDisOpinionController.do?saveOpinion`, data });
//获取工单类型列表
const getWorkTypeList = data => server({ url: `${iomsApi}/appOlgTaskManagement.do?getWorkTypeList`, data });
//获取公告列表 appNoticeController.do?getNotice
const getNotice = data => server({ url: `${iomsApi}/appNoticeController.do?getNotice`, data });
//获取公告详情 appNoticeController.do?getNoticeDetail
const getNoticeDetail = data => server({ url: `${iomsApi}/appNoticeController.do?getNoticeDetail`, data });
//appDisNoticeController.do?getNoticeList
const getNoticeList = data => server({ url: `${iomsApi}/appDisNoticeController.do?getNoticeList`, data });
const getDisNoticeDetail = data => server({ url: `${iomsApi}/appDisNoticeController.do?getNoticeDetail`, data });

// 工单类型配置提醒 appOlgTaskManagement.do?getTaskWorkConfiguration
const getTaskWorkConfiguration = data =>
  server({
    url: `${iomsApi}/appOlgTaskManagement.do?getTaskWorkConfiguration`,
    data
  });

//appOlgTaskManagement.do?getCustomizeTaskConfiguration
const getCustomizeTaskConfiguration = data =>
  server({
    url: `${iomsApi}/appOlgTaskManagement.do?getCustomizeTaskConfiguration`,
    data
  });

//公车预定 查询公车列表
const getOfficialCarList = data =>
  server({
    url: `${iomsApi}/officialCarReserveController/getOfficialCarList`,
    data
  });
//根据车辆id获取预约详情
const getOfficialCarDateById = data =>
  server({
    url: `${iomsApi}/officialCarReserveController/getOfficialCarDateById`,
    data
  });
//获取未完工工单
const getUnfinishedMineTask = data =>
  server({
    url: `${iomsApi}/appOlgTaskManagement.do?getUnfinishedMineTask`,
    data
  });

//催单
const urgeOderByWeChat = data => server({ url: `${iomsApi}/appOlgTaskManagement.do?urgeOderByWeChat`, data });
//获取登录人员信息
const getNewStaffInfoById = data =>
  server({
    url: `${iomsApi}/iHCRSStatisticsController/getNewStaffInfoById`,
    data
  });
//保存用户openid
const savaTeamMember = data => server({ url: `${iomsApi}/teamMember/save`, data });
//新增工单 车辆预定
// const saveTaskByWeChat = data => server({ url: `${iomsApi}/appOlgTaskManagement.do?saveTaskByWeChat`, data });
// 到达
const arrive = data => server({ url: `${iomsApi}/appDisOlgTask.do?updateTaskArriveState`, data });

// 设备管理--任务数量统计
const taskStatistics = data => server({ url: `${ipsmApi}/planTaskNew/taskStatistics`, data });
// 设备管理--今日任务列表
const todayTaskList = data => server({ url: `${ipsmApi}/planTaskNewApiController/getAppTaskList`, data });
// 查询任务统计列表（无权限）
const taskListByAnalysis = data => server({ url: `${ipsmApi}/planTaskStatistics/getAppTaskStatisticsList`, data });
// 设备管理--任务分类/数量
const taskType = data => server({ url: `${ipsmApi}/plan/getTaskTemplate`, data });
// 设备管理--设备类别/数量
const deviceType = data => server({ url: `${ipsmApi}/plan/getTaskEquipment`, data });
// 综合巡检--任务分类
const inspectionType = data => server({ url: `${ipsmApi}/plan/comprehensiveTask `, data });
// 设备管理--任务列表
const taskList = data =>
  server({
    url: `${ipsmApi}/planTaskNewApiController/getInspectionTaskList`,
    data
  });
// 设备管理--归口部门
const competentList = data => server({ url: `${ipsmApi}/asset/assetDetails/getEquipmentType`, data });
// 设备管理--资产列表
const deviceList = data => server({ url: `${ipsmApi}/asset/assetDetails/getAssetListByH5`, data });
// 设备管理--获取设备详情
const deviceDetails = data => server({ url: `${ipsmApi}/asset/assetDetails/getAssetDetailsById`, data });
//设备详情
const deviceDetailsCopy = data => server({ url: `${ipsmApi}/asset/assetDetails/getAssetDetails`, data });
// 资产管理--获取资产概览
const getAssetOverviewPieChart = data => server({ url: `${ipsmApi}/asset/assetDetails/getAssetOverviewPieChart`, data });
// 资产管理--获取资产列表
const getElevatorAssetList = data => server({ url: `${ipsmApi}/asset/assetDetails/getElevatorAssetList`, data });
// 设备管理--运维记录
const maintenanceList = data =>
  server({
    url: `${ipsmApi}/operation/record/getOperationRecordsByAssetId`,
    data
  });
// 设备管理--任务详情
const taskDetail = data =>
  server({
    url: `${ipsmApi}/planTaskNewApiController/taskPointByTaskId`,
    data
  });
// 设备管理--任务书详情
const taskBookDetail = data => server({ url: `${ipsmApi}/taskPointRelease/appDetail`, data });
// 自定义巡检点详情
const zdyPointDetail = data => server({ url: `${ipsmApi}/taskPoint/get`, data });
// 设备管理--巡检提交
const inspectionSubmit = data => server({ url: `${ipsmApi}/appWorkSubmission/submissionQualified`, data });
// 设备管理--扫码
const getPerformTask = data => server({ url: `${ipsmApi}/planTaskNewApiController/getPerformTask`, data });
// 图片上传
const uploadImg = data => server({ url: `${ipsmApi}/file/upload`, data });
// 巡检报修
const taskRepair = data => server({ url: `${iomsApi}/appOlgTaskManagement.do?saveTaskByWeChat`, data });
// 任务统计
const taskAnalysis = data => server({ url: `${ipsmApi}/planTaskNew/getTaskEquipmentMap`, data });
// 任务完成率统计
const taskComplete = data =>
  server({
    url: `${ipsmApi}/maintainController/taskPercentageByStatusApp`,
    data
  });
// 任务统计图表
const taskAnalysisCharts = data =>
  server({
    url: `${ipsmApi}/maintainController/taskPercentageByTypeApp`,
    data
  });
// 任务统计列表
const taskAnalysisList = data => server({ url: `${ipsmApi}/maintainController/planPercentageTop5App`, data });

// 服务总览
const getServiceOverview = data =>
  server({
    url: `${iomsApi}/iHCRSStatisticsController/getServiceAnalysis`,
    data
  });
const getOrderCountByUserInfo = data =>
  server({
    url: `${iomsApi}/iHCRSStatisticsController/getOrderCountByUserInfo`,
    data
  });
const checkAndAccept = data => server({ url: `${iomsApi}/appDisOlgTask.do?checkAndAccept`, data });
const getNewSysConfigParam = data => server({ url: `${iomsApi}/appDisOlgTask/getNewSysConfigParam`, data });

const getDictionary = data => server({ url: `${iomsApi}/deviceRepair/getDictionary`, data });
const getItemList = data => server({ url: `${iomsApi}/workOrderConfigController/getItemList`, data });
const getAssetName = data =>
  server({
    url: `${ipsmApi}/operation/record/getAssetName
`,
    data
  });

// 风险管理--重点台账
const getAccountList = data => server({ url: `${ipsmdbApi}/standingBook/listData`, data });

// auth-web
const getMessageListAppByCategory = data => postRequest({ url: `${authwebApi}/message/getMessageList`, data });
const approvalBacklog = data => postRequest({ url: `${sApi}/backlog/approvalBacklog`, data });//审批列表新接口
// 消息已读
const finishToDoListLevel = data => postRequest({ url: `${newsApi}/message/finishToDoListLevel`, data });
// 数据中心--医废类型分析
const getMedicalWasteType = data =>
  server({
    url: `${imwsapi}/iHCRSStatisticsController/getAppTypeAnalysisInfo`,
    data
  });
// 数据中心--医废科室top10
const getMedicalTopten = data =>
  server({
    url: `${imwsapi}/iHCRSStatisticsController/getDepartProduceList`,
    data
  });
// 数据中心--医废事项分析
const getMedicalWasteOption = data =>
  server({
    url: `${imwsapi}/iHCRSStatisticsController/getDepartProduceInfo`,
    data
  });
// 数据中心--维修区域
const getRepairArea = data => server({ url: `${iomsApi}/appDisStatistic.do?getInterspaceRepairs`, data });
// 数据中心--维修成本分析
const getMaintenanceCosts = data =>
  server({
    url: `${iomsApi}/iHCRSStatisticsController/maintenanceCosts`,
    data
  });
// 数据中心--维修事项分析
const getFixCostAnalysis = data => server({ url: `${iomsApi}/appDisStatistic.do?repairItemsService`, data });
// 数据中心--耗材分析
const getConsumableNum = data =>
  server({
    url: `${iomsApi}/iHCRSStatisticsController/getConsumableNum`,
    data
  });
// 数据中心--班组维修量
const getDepartmentMaintenance = data =>
  server({
    url: `${iomsApi}/iHCRSStatisticsController/getDepartmentMaintenance`,
    data
  });

// 空间管理
const spaceList = data => postRequest({ url: `${spaceApi}/space/spaceInfo/list`, data });
// 空间功能类型
const getSuperiorData = data => getRequest({ url: `${spaceApi}/web/prDictionaryDetailsD/getSuperiorData`, data });
// 空间管理列表
const spaceSelectByList = data =>
  postRequest({
    url: `${spaceApi}/dictionary/sys-value-dict/selectByList`,
    data
  }); // 空间管理列表
const spacePageList = data => postRequest({ url: `${spaceApi}/space/modifyHistory/pageList`, data }); // 空间变更记录
// 数据中心--工单列表
const getWorkOrderList = data => server({ url: `${iomsApi}/appOlgTaskManagement.do?getMineTaskForApp`, data });
// app获取菜单权限
const getMenuAuth = data => postRequest({ url: `${authwebApi}/SysMenu/indexApp`, data });

// 问卷
const selfQuestionListToWx = data =>
  postParamsQS({
    url: `${quesApi}/questionPvq/recovery/selfQuestionListToWx`,
    data
  }); //查看个人问卷列表
const selfQuestionCount = data =>
  postParamsQS({
    url: `${quesApi}/questionPvq/recovery/selfQuestionCount`,
    data
  }); //移动端获取用户要回答问题的总数
const getPaperQuestions = data => postParamsQS({ url: `${quesApi}/questionPvq/getPaperQuestions`, data });
const saveAnswer = data => postParamsQS({ url: `${quesApi}/questionPvq/saveAnswer`, data });

// 设备报修
const subDeviceRepair = data => server({ url: `${iomsApi}/deviceRepair/createWorkOrder`, data });
// 保存操作记录
const saveOperateRecord = data => server({ url: `${ipsmApi}/operation/record/saveOperationRecords`, data });
// 查询设备是否在修
const inquireRepair = data => server({ url: `${iomsApi}/deviceRepair/getDeviceWorkOrderStatus`, data });
// 查询工单是否确认
const isConfirmOrder = data => server({ url: `${iomsApi}/deviceRepair/getAcceptOrderConfig`, data });
// 工单确认
const confirmOrder = data => server({ url: `${iomsApi}/deviceRepair/confirmAcceptOrder`, data });
//  数据中心--报警警中心列表
const selectAlarmRecordAll = data => postRequest({ url: `${alarmApi}/alarm/record/selectAlarmRecordAll`, data });
//  数据中心--报警警中心经典案例
const selectClassicsAlarm = data => postRequest({ url: `${alarmApi}/alarm/record/selectAlarmRecordClassics`, data });
// 巡检扫码
const inspectionScanCode = data => server({ url: `${ipsmApi}/planTaskNewApiController/getPerformTask`, data });
//  医帮手app饼状图统计
const getPolicePieByApp = data => getRequest({ url: `${alarmApi}/alarm/record/getPolicePieByApp`, data });
//  医帮手appTop报警统计
const getAlarmTopCount = data => getRequest({ url: `${alarmApi}/alarm/record/getAlarmTopCount`, data });
//  医帮手app根据报警来源统计
const getAlarmSourceCount = data => getRequest({ url: `${alarmApi}/alarm/record/getAlarmSourceCount`, data });
//  医帮手app报警趋势
const getAlarmTrend = data => getRequest({ url: `${alarmApi}/alarm/record/getAlarmTrend`, data });
//  医帮手app查询报警数量统计
const getPoliceInfoByApp = data => getRequest({ url: `${alarmApi}/alarm/record/getPoliceInfoByApp`, data });

// -----------------------------------------------------IEMC-----------------------------------------------------
// 排污总量和给水总量
const getWaterSupplyTotal = data => postRequest({ url: `${iemcApi}/waterSupply/waterSupplyTotal`, data });
// 冷热源运行总览
const getColdHostStatistics = data => postRequest({ url: `${iemcApi}/coleHot/statisticsOverview`, data });
// 给排水运行总览
const getWaterOverview = data => postRequest({ url: `${iemcApi}/waterSupply/waterOverview`, data });
// 根据实体类型查询监测参数列表
const getRealEnvMonitoringListApp = data =>
  postRequest({
    url: `${iemcApi}/realMonitoring/getRealEnvMonitoringListApp`,
    data
  });
// 监测参数列表
const getRealMonitoringList = data =>
  // postRequest({ url: `${iemcApi}/realMonitoring/getRealMonitoringList`, data });
  postRequest({
    url: `${iemcApi}/realMonitoring/getRealMonitoringListSecurity`,
    data
  });
// 监测参数列表
const getRealMonitoring = data => postRequest({ url: `${iemcApi}/realMonitoring/getRealMonitoringList`, data });
// 获取分组列表
const getEntityMenuList = data => postRequest({ url: `${iemcApi}/entityMenu/getEntityMenuList`, data });
const getSurveyStatusByImhCode = data =>
  getRequest({
    url: `${iemcApi}/surveyAndParameter/getSurveyStatusByImhCode`,
    data
  });
// 根据一级分组查询childList
const getChildMenuCodesByProjectCode = data =>
  postRequest({
    url: `${iemcApi}/entityMenu/getChildMenuCodesByProjectCode`,
    data
  });
//  照明运行总览统计
const countOverview = data =>
  postRequest({
    url: `${iemcApi}/lightingOperationMonitoring/countOverview`,
    data
  });
//  照明运行总览统计
const groupOperationMonitoring = data =>
  postRequest({
    url: `${iemcApi}/lightingOperationMonitoring/groupOperationMonitoring`,
    data
  });
const getLightingBySpaceId = data =>
  postRequest({
    url: `${iemcApi}/lightingOperationMonitoring/getLightingBySpaceId`,
    data
  });
const getLightingByGroupId = data =>
  postRequest({
    url: `${iemcApi}/lightingOperationMonitoring/getLightingByGroupId`,
    data
  });
// 根据projectCode获取报警数量
const getPoliceCount = data => postRequest({ url: `${iemcApi}/surveyAndParameter/selectPoliceCount`, data });
// 获取故障离线清单
const getSelectStatusList = data => postRequest({ url: `${iemcApi}/surveyAndParameter/selectStatusList`, data });
// 实时监测--scada列表
const getScadaList = data => postRequest({ url: `${iemcApi}/entityMenu/getScalaEntityMenuList`, data });
const structureTree = data => getRequest({ url: `${spaceApi}/space/structure/structureTree`, data });
const selectByList = data =>
  postRequest({
    url: `${spaceApi}/dictionary/sys-value-dict/selectByList`,
    data
  });

// 环境监测总览
const GetSettingOverview = data => postRequest({ url: `${iemcApi}/environment/getEnvironmentStatistics`, data });
// 环境监测详情
const GetSettingDetails = data =>
  postRequest({
    url: `${iemcApi}/environment/selectAppEnvironmentInfoByParamId`,
    data
  });
// 冷热源统计
const GetColdOverview = data => postRequest({ url: `${iemcApi}/coleHot/selectOverview`, data });

// 布草统计--布草总数和类型
const linenSumType = data =>
  getRequestLinen({
    url: `${linenApi}/linenStatistics/getLinenPieChart`,
    data
  });
// 布草使用状态
const linenStatus = data =>
  getRequestLinen({
    url: `${linenApi}/linenStatistics/getLinenWashingStatus`,
    data
  });
// 布草科室排行
const linenRanking = data => getRequestLinen({ url: `${linenApi}/linenStatistics/getBarChart`, data });
// 布草科室排行
const linenAgeing = data =>
  getRequestLinen({
    url: `${linenApi}/linenStatistics/getCollectionTrend`,
    data
  });
//空调运行时长统计
const airRunTime = data => postRequest({ url: `${iemcApi}/airCondition/airRunTime`, data });
//空调运行率占比排行（%）
const airRunRate = data => postRequest({ url: `${iemcApi}/airCondition/airRunRate`, data });
//查看空调 运行时长|运行率|报警|故障|离线 的单个详情
const airViewDetail = data => postRequest({ url: `${iemcApi}/airCondition/airViewDetail`, data });
// 统计空调报警
const airRunPolice = data => postRequest({ url: `${alarmApi}/alarm/record/airRunPolice`, data });
// 运行总览统计
const querySurveyListByMenuCode = data => postRequest({ url: `${iemcApi}/aircooledheatpump/querySurveyListByMenuCode`, data });
// app运行监测查询离线|故障数
const airViewDetailApp = data => postRequest({ url: `${iemcApi}/airCondition/airViewDetailApp`, data });
// 能耗登录
const KgceLogin = data => postRequestKgce({ url: `${kgceApi}/sys/login`, data, headers: {} });
// 能源及分类的树结构
const GetEnergyAndClassifyTree = data =>
  getRequestKgce({
    url: `${kgceApi}/gy-service-energy-core/entSysEnergyCategoryInfo/getEnergyCategoryTree`,
    data
  });
// 获取模型能源数据列表
const GetModelEnergyDataList = data =>
  getRequestKgce({
    url: `${kgceApi}/gy-service-energy-core/entEnergyData/getModelEnergyDataList`,
    data
  });
// 校验任务是否可执行
const executeTesk = data =>
  postRequest({
    url: `${ipsmApi}/planTaskNewApiController/executePlanTaskByTaskId`,
    data
  });
// 给排水运行总览
const airGetEntityMenuList = data => postRequest({ url: `${iemcApi}/airCondition/getEntityMenuList`, data });

// 用氧统计
const GetAirStatistics = data => postRequest({ url: `${iemcApi}/gasesStatistic/getAirStatistics`, data });
// 获取服务事项
const getOlgTransportItem = data =>
  server({
    url: `${iomsApi}/iHCRSStatisticsController/getOlgTransportItem`,
    data
  });

// 一级菜单
const GetParentMenuList = data => postRequest({ url: `${iemcApi}/entityMenu/getParentMenuList`, data });
// 配电环境监测信息
const GetEnvironmentMonitor = data => postRequest({ url: `${iemcApi}/surveyAndParameter/selectEnvironSurvey`, data });
// 配电设备运行总览
const GetEquipmentOperationMonitor = data => postRequest({ url: `${iemcApi}/surveyAndParameter/selectEntityType`, data });
// 配电重点设备统计
const GetKeyDeviceCount = data => postRequest({ url: `${iemcApi}/airCondition/countKeyDeviceByProjectCode`, data });
// 配电详情
const GetBoilerMonitorDetail = data => postRequest({ url: `${iemcApi}/boiler/queryBoilerMonitorDetail`, data });
// 获取安防监测项列表
const GetRealMonitoringListSecurity = data => postRequest({ url: `${iemcApi}/realMonitoring/getRealMonitoringListSecurity`, data });

const GetDeviceOperating = data => postRequest({ url: `${iemcApi}/surveyAndParameter/selectInfoByType`, data });

// 空间申请-查询申请列表
const GetSpaceApplyList = data => postRequest({ url: `${sApi}/spaceApply/querySpaceApplyListByPage`, data });
// 空间申请-新增
const AddSpaceApply = data => postRequest({ url: `${sApi}/spaceApply/addSpace`, data });
// 空间申请-用途字典
const GetDictionaryList = data => postRequest({ url: `${sApi}/spaceApply/getDictionaryList`, data });
// 空间申请-申请详情
const GetSpaceApplyDetails = data => postRequest({ url: `${sApi}/spaceApply/queryApplyById/${data}` });
// 空间申请-删除申请
const DeleteApplyById = data => postRequest({ url: `${sApi}/spaceApply/deleteApplyById/${data}` });
// 空间申请-空间申请单状态更改
const UpdateSpaceApply = data => postRequest({ url: `${sApi}/spaceApply/updateApplyStatusByApplyId`, data });

// 锅炉监测-运行总览-数据分析
const GetEnergyStatistics = data => postRequest({ url: `${iemcApi}/boiler/energyStatistics`, data });
// 锅炉监测-运行总览-锅炉监测
const GetBoilerMonitorList = data => postRequest({ url: `${iemcApi}/boiler/queryBoilerMonitor`, data });
// 锅炉监测-运行总览-燃气监测
const GetGasMonitoring = data => postRequest({ url: `${iemcApi}/boiler/queryGasMonitoring`, data });
// 锅炉监测-运行总览-查询锅炉总数
const GetBoilerCount = data => postRequest({ url: `${iemcApi}/boiler/boilerCount`, data });
// 监测报警统计
const GetAirRunPoliceDetail = data => postRequest({ url: `${alarmApi}/alarm/record/airRunPoliceDetail`, data });
// 锅炉监测-运行总览-环境监测详情
const getBoilerByProjectCode = data => postRequest({ url: `${alarmApi}/alarm/record/getBoilerByProjectCode`, data });
// 锅炉监测详情
const queryBoilerMonitorDetail = data => postRequest({ url: `${iemcApi}/boiler/queryBoilerMonitorDetail`, data });
// 锅炉监测-运行总览-数据分析详情
const energyStatisticsDetail = data => postRequest({ url: `${iemcApi}/boiler/energyStatisticsDetail`, data });

// 安消防监测-总览-报警趋势总览统计
const countWarnByTime = data => postRequest({ url: `${iemcApi}/electricitySafe/countWarnByTime`, data });
// 安消防监测-总览-报警趋势折线图
const queryAlarmTrendFromTime = data => postRequest({ url: `${iemcApi}/electricitySafe/queryAlarmTrendFromTime`, data });
// 安消防监测-总览-实体列表
const querySurveyListPageByMenuCode = data => postRequest({ url: `${iemcApi}/electricitySafe/querySurveyListPageByMenuCode`, data });

// 空间清查-获取任务列表
const GetSpaceCheckList = data => postRequest({ url: `${sApi}/space/queryInventoryListByPageForApp`, data });
// 空间清查-获取任务列表
const GetDeptTaskDetails = data => postRequest({ url: `${sApi}/space/queryDepartListByPageForApp`, data });
// 空间清查-获取空间列表
const GetDeptSpaceDetails = data => postRequest({ url: `${sApi}/space/querySpaceListByPageForApp`, data });
// 空间清查-手动登记
const AddRegistSpaceInfo = data => postRequest({ url: `${sApi}/space/registSpaceInfo`, data });
// 空间清查-手动登记列表
const GetRegistSpaceInfo = data => postRequest({ url: `${sApi}/space/queryRegistSpaceInfo`, data });
// 空间清查-获取空间数量
const GetSpaceNum = data => postRequest({ url: `${sApi}/space/querySpaceNum`, data });
// 空间清查-删除手动登记
const DeleteRegistInfoById = data => postRequest({ url: `${sApi}/space/deleteRegistInfoById/${data}` });
// 空间清查-更新空间盘点
const UpdateSpaceStatus = data => postRequest({ url: `${sApi}/space/updateSpaceStatusForApp`, data });
// 停车场管理
const getOperateTotalDetail = data => postRequest({ url: `${quesApi}/parking/operateTotalDetail`, data }); // 运营总览
const getParkingSuperviseInfo = data => getRequest({ url: `${quesApi}/parking/parkingSuperviseInfo`, data }); // 车场监管统计
const getExistChargeDetail = data =>
  getRequest({
    url: `${quesApi}/parking/existChargeDetail`,
    data
  }); // 出口收费统计
const pageListByBedId = data => getRequest({ url: `${quesApi}/merchant/pageListByBedId`, data }); // 车场监管统计
// 用电
const querySurveyDetail = data => postRequest({ url: `${iemcApi}/electricitySafe/querySurveyDetail`, data }); // 实体详情查询
const querySurveyCount = data => postRequest({ url: `${iemcApi}/electricitySafe/querySurveyCount`, data }); // 监控点数等统计
const queryMenuByPage = data => postRequest({ url: `${iemcApi}/electricitySafe/querySurveyListPageByMenuCode`, data }); // 2、串口查询
const queryMenuGroup = data => postRequest({ url: `${iemcApi}/electricitySafe/queryMenuGroup`, data }); // 7、APP-监控区---查询分组列表
const countMonitorPointByMenuCode = data => postRequest({ url: `${iemcApi}/electricitySafe/countMonitorPointByMenuCode`, data }); // 7、APP-监控区---查询分组列表

// 抄表管理相关接口
const dormDialList = data => postParamsQS({ url: `${dorm}/appRecordForm/newRecordFormList`, data }); // 抄表list
const dialInfo = data => postParamsQS({ url: `${dorm}/appRecordForm/upRecord`, data }); // 上次抄表信息
const userList = data => postParamsQS({ url: `${dorm}/appRecordForm/getInOUtUser`, data }); // 人员信息
const addDial = data => postParamsQS({ url: `${dorm}/appRecordForm/add`, data }); // 添加抄表信息
const dialRecordsList = data => postParamsQS({ url: `${dorm}/appRecordForm/recordFormByDormId`, data }); // 抄表记录
// 床位人员信息
const bedUserInfo = data => getRequest({ url: `${dorm}/dorm/qrCodeBedInfo`, data }); // 抄表记录
//-----工程
const examineApproveList = data => postProject({ url: `${imem}/app/approval/list`, data }); // 工程管理审批列表
const checkUserExists = data => getProject({ url: `${imem}/api/user/checkUserExists`, data }); // 检查用户是否存在
const getApprovalByType = data => postProject({ url: `${imem}/app/approval/getApprovalByType`, data }); // 根据类型展示不同的审批信息
const getDict = data => postProject({ url: `${imem}/app/dataDictionary/getDict`, data }); // 字典
const getAppr = data => postProject({ url: `${imem}/app/approval/appr`, data }); // 审批提交
const getContract = data => postProject({ url: `${imem}/app/approval/getContract`, data });
const getByProjectId = data => getProject({ url: `${imem}/app/dataProjectMembers/getByProjectId`, data }); //根据项目Id查询协办人
const getListByProject = data => postProject({ url: `${imem}/app/acceptanceItems/listByProject`, data }); //根据项目查询质量列表
const getListByProjectId = data => postProject({ url: `${imem}/app/acceptanceItems/listByProjectId`, data }); //根据项目id查询质量列表
const getProjectList = data => postProject({ url: `${imem}/app/project/list`, data }); //项目管理列表分页
const getProjecDetails = data => getProject({ url: `${imem}/app/project/get`, data }); //项目管理根据id查询办人
const getStatements = data => postProject({ url: `${imem}/app/project/statement`, data }); //项目管理报表
const getProjectListBy = data => postProject({ url: `${imem}/app/rectification/listByProject`, data }); //根据项目查询隐患整改列表
const getProjectListById = data => postProject({ url: `${imem}/app/rectification/listByProjectId`, data }); //根据项目id查询隐患整改列表
const getByProjectList = data => postProject({ url: `${imem}/app/contract/listByProject`, data }); //根据项目查询合同管理列表
const getlistByProjectId = data => postProject({ url: `${imem}/app/contract/listByProjectId`, data }); //根据项目id查询合同列表
const getRectification = data => getProject({ url: `${imem}/app/rectification/get`, data }); //根据id查询整改单
const getPrintRectification = data => getProject({ url: `${imem}/app/rectification/getPrintRectification`, data }); //整改单
const getCountByProject = data => postProject({ url: `${imem}/app/intermediateAcceptance/countByProject`, data }); //根据不同项目展示中间验收个数
const getIntermedList = data => postProject({ url: `${imem}/app/intermediateAcceptance/listByProject`, data }); //根据项目中间验收列表
const addContractMark = data => postProject({ url: `${imem}/app/contractProblem/save`, data }); //新增问题合同记录
const addIntermediate = data => postProject({ url: `${imem}/app/intermediateAcceptance/save`, data }); //新增中间验收
const getNoList = data => postProject({ url: `${imem}/app/project/noList`, data }); //新增中间验收
const getDepartmentList = data => postProject({ url: `${imem}/app/org/list`, data }); //
const getDetailsContract = data => getProject({ url: `${imem}/app/contract/get`, data }); //新增中间验收
const getcontractlist = data => postProject({ url: `${imem}/app/contract/list`, data }); //合同表分页
const getSearch = data => postProject({ url: `${imem}/app/project/search`, data }); //合同表分页
const getPaymentNodeTaskListByProjectId = data => getProject({ url: `${imem}/app/paymentNodeTask/getPaymentNodeTaskListByProjectId`, data }); //合同表分页
const getEditSave = data => postProject({ url: `${imem}/app/contract/editSave`, data }); //合同表分页
const getQualityInspectionAdd = data => postProject({ url: `${imem}/app/acceptanceItems/save`, data }); //合同表分页
const getScceptanceItems = data => getProject({ url: `${imem}/app/acceptanceItems/get`, data }); //合同表分页
const getEditSaves = data => postProject({ url: `${imem}/app/acceptanceItems/editSave`, data }); //合同表分页
const getApprSave = data => postProject({ url: `${imem}/app/rectification/apprSave`, data }); //代办整改单
const getContractSubmit = data => postProject({ url: `${imem}/app/contractPaymentReceived/submit`, data });
const getAcceptance = data => getProject({ url: `${imem}/app/intermediateAcceptance/get`, data }); //根据id查询中间验收表
const getAcceptanceEditSave = data => postProject({ url: `${imem}/app/intermediateAcceptance/editSave`, data }); //修改中间验收表
const getAcceptanceDelete = data => postProject({ url: `${imem}/app/intermediateAcceptance/delete`, data }); //删除中间验收
const getDisposeContract = data => postProject({ url: `${imem}/app/contractProblem/disposeContract`, data }); //删除中间验收

// 演练任务列表
const getQueryTaskData = data => postRequest({ url: `${planApi}/preplanDrillTask/queryTaskByPage`, data });
const getTaskDataById = data => postRequest({ url: `${planApi}/preplanDrillTask/getTaskById`, data }); //演练任务详情
const updateTaskData = data => postRequest({ url: `${planApi}/preplanDrillTask/updateTask`, data }); //演练任务评价
const updateTaskNoticeData = data => postRequest({ url: `${planApi}/preplanDrillTask/updateTaskNotice`, data }); // 更改演练通知
const sureTaskNoticeData = data => postRequest({ url: `${planApi}/preplanDrillTask/sendTaskNotice`, data }); // 确认演练

// 常规预案
const GetPlanList = data => postRequest({ url: `${planApi}/preplanBasic/getConfigPage`, data }); // 预案列表
const GetPlanDetail = data => postRequest({ url: `${planApi}/preplanBasic/getBasicConfigDetail`, data }); // 预案详情

// 人员列表
const staffList = data => postRequest({ url: `${spaceApi}/hospitalStaff/hospital-staff/list`, data }); // 对应人员

const voiceId2url = data => postParamsQS({ url: `${authwebApi}/wechat/handleVoice`, data });
const taskPointAmountStatistics = data => server({ url: `${ipsmApi}/planTaskNew/taskPointAmount`, data }); // 运维记录巡检点统计
const fromPointList = data => server({ url: `${ipsmApi}/planTaskNewApiController/getTaskPointList`, data }); // 根据巡检点查询任务列表

//数据统计

const getServiceStatusData = data => postParamsQS({ url: `${iomsApi}/iHCRSStatisticsController/getServiceAnalysisForStatus`, data }); // 医帮手服务总览-工单状态统计
const getServiceInfoData = data => postParamsQS({ url: `${iomsApi}/iHCRSStatisticsController/getServiceAnalysisForApp`, data }); // 医帮手服务总览-工单总数统计
const getEquipmentTypeStatisticsData = data => server({ url: `${ipsmApi}/asset/assetDetails/getProfessionalCategory`, data }); // 医帮手服务总览-设备分类统计
const getEquipmentStatusStatisticsData = data => server({ url: `${ipsmApi}/asset/assetDetails/equipmentStatusStatistics`, data }); // 医帮手服务总览-设备状态统计

//站式扫码提交文件
const submitScanFile = data => postRequestNoToken({ url: `${alarmApi}/wartime/submit`, data }); // 提交报警处理现场文件
const checkScanFile = data => postRequestNoToken({ url: `${alarmApi}/wartime/checkAlarmAffirm`, data }); // 报警状态检查
const checkIsTimeout = data => postRequestNoToken({ url: `${alarmApi}/wartime/queryQrCodeExpire`, data }); // 报警状态检查

// 获取空间信息
const lookUpById = data => getRequest({ url: `${spaceApi}/space/spaceInfo/lookUpById`, data });

//报警中心
const queryAllAlarmStatisticsCount = data => postRequest({ url: `${alarmApi}/alarm/record/queryAllAlarmStatisticsCount`, data });
const selectAlarmRecordById = data => postRequest({ url: `${alarmApi}/alarm/record/selectAlarmRecordById`, data }); //查询报警记录详情-通过id
const queryLimAlarmFile = data => postRequest({ url: `${alarmApi}/alarmFile/queryLimAlarmFile`, data }); //查询报警录像列表
const getShield = data => postRequest({ url: `${alarmApi}/alarm/record/shield`, data }); //屏蔽
const insertRemarkById = data => postRequest({ url: `${alarmApi}/alarm/operation/insertRemarkById`, data }); //添加备注信息
const handleAlarmAffirmById = data => postRequest({ url: `${alarmApi}/alarm/record/handleAlarmAffirmById`, data }); //处理报警-通过id
const OneKeyDispatch = data => postRequest({ url: `${alarmApi}/alarm/record/oneKeyDispatch`, data }); // 一键派单
const setAlarmRecordToClassics = data => postRequest({ url: `${alarmApi}/alarm/record/setAlarmRecordToClassics`, data }); // 将报警记录存为经典案例
const getSelected = data => getRequest({ url: `${spaceApi}/unitManager/unit-manager/getSelected`, data }); // 查询医院
const getSelectedDept = data => getRequest({ url: `${spaceApi}/departmentManager/department-manager/getSelectedDept`, data }); //  根据所属单位回去部门列表
const summarySave = data => postRequest({ url: `${alarmApi}/alarm/alarmSummary/save`, data }); // 总结分析
const getAlarmThirdSystemList = data => postRequest({ url: `${alarmApi}/alarm/fieldsConfig/getAlarmThirdSystemList`, data }); // 获取报警系统数据
const queryFieldsConfigList = data => postRequest({ url: `${alarmApi}/alarm/fieldsConfig/queryFieldsConfigList`, data }); // 获取报类型数据
const getInspectionTaskVoPage = data => postRequest({ url: `${alarmApi}/alarm/record/getInspectionTaskVoPage`, data }); // 分页查询巡检记录-通过报警设备ID
//领用申请单
const findDepotManagementStockPage = data => postParamsQS({ url: `${iomsApi}/iHCRSStatisticsController/findDepotManagementStockPage`, data }); // 分物料管理列表
const outDepotManagementStock = data => postRequest({ url: `${iomsApi}/iHCRSStatisticsController/outDepotManagementStock`, data }); // 物料管理出库
const findDepotManagementRecordPage = data => postParamsQS({ url: `${iomsApi}/iHCRSStatisticsController/findDepotManagementRecordPage`, data }); // 变动记录列表

// 领用部门
const getOutsourcingCompanyInfo = data => postParamsQS({ url: `${iomsApi}/iHCRSStatisticsController/getOutsourcingCompanyInfo`, data }); // 公司
const getWorkTypeListTeam = data => postParamsQS({ url: `${iomsApi}/team/getAllTeamList`, data }); // 班组
const getServiceWorkers = data => postParamsQS({ url: `${iomsApi}/iHCRSStatisticsController/getOCTeamMemberInfo`, data }); // 根据服务部门获取指派工人
const getOutDepotNumberByWorkNum = data => postRequest({ url: `${iomsApi}/iHCRSStatisticsController/getOutDepotNumberByWorkNum`, data }); // 根据工单号查询出库单号
// 空间设施登记
const spaceDeviceList = data => postRequest({ url: `${sApi}/space/facility/queryFacilityDeviceTreeNotDictByApp`, data }); // 空间设备列表
const getFacilityDetails = data => postRequest({ url: `${sApi}/spaceFacilityRegistration/getFacilityDetails`, data }); // APP查询设施未使用字典类型
const otherEquList = data => postRequest({ url: `${sApi}/spaceFacilityRegistration/getSecondTypeById`, data }); // 其他类型设备列表
const changeViewStatus = data => postRequest({ url: `${sApi}/spaceFacilityRegistration/viewed`, data }); // 保存更改查看状态
const saveRegistration = data => postRequest({ url: `${sApi}/spaceFacilityRegistration/saveRegistration`, data }); // 保存更改查看状态
const getDeviceCountGroupedByFacilityType = data => postRequest({ url: `${sApi}/space/facility/getDeviceCountGroupedByFacilityType`, data }); // 查询设备字典
const changeRecord = data => postRequest({ url: `${sApi}/facility/record/getFacilityRecordsApp`, data }); // 保存更改查看状态
const submitRecord = data => postRequest({ url: `${sApi}/space/facility/getSpaceFacilityRegistrationList`, data }); // 提交记录
const submitRecordDetail = data => postRequest({ url: `${sApi}/space/facility/getTypeById`, data }); // 提交记录详情
const getSpaceFacilityConfig = data => getRequest({ url: `${sApi}/space/config/getSpaceFacilityConfig`, data }); // 查询审核配置信息
const queryFacilityDeviceTreeByApp = data => postRequest({ url: `${sApi}/space/facility/queryFacilityDeviceTreeByApp`, data }); // 查询空间设施App端
const queryFacilityDeviceByApp = data => postRequest({ url: `${sApi}/space/facility/queryFacilityDeviceByApp`, data }); // 根据二级字典编码查询三级字典关联设施信息 ———— App端
const saveDeviceBuffer = data => postRequest({ url: `${sApi}/space/buffer/saveDeviceBuffer`, data }); // 设备保存
//协和一站式
const queryApprovalList = data => postParamsQS({ url: `${iomsApi}/approval/control/queryApprovalList`, data }); // 查询审批列表
const saveApprovalControl = data => postParamsQS({ url: `${iomsApi}/approval/control/saveApprovalControl`, data }); // 挂单提交审批流程
const queryDetailList = data => postParamsQS({ url: `${iomsApi}/approval/detail/queryDetailList`, data }); // 根据审批ID查询审批节点详情
const approveOrReject = data => postParamsQS({ url: `${iomsApi}/approval/control/approveOrReject`, data }); // 审批操作
const revokeApproval = data => postParamsQS({ url: `${iomsApi}/approval/control/revokeApproval`, data }); // 撤销当前审批流程
const confirmArrival = data => postRequest({ url: `${iomsApi}/appOlgTaskManagement/confirmArrival`, data }); // 确认到达
const queryArrival = data => postRequest({ url: `${iomsApi}/appOlgTaskManagement/getConfirmArrivalStatus`, data }); // 查询工单是否到达
//协和一站式第三版
const saveInventoryApprovalControl = data => postParamsQS({ url: `${iomsApi}/approval/inventory/saveApprovalControl`, data }); // 出入库提交审批
const queryMyApprovalList = data => postParamsQS({ url: `${iomsApi}/approval/inventory/queryMyApprovalList`, data }); // 查询出入库审批列表
const queryApprovalControlById = data => postParamsQS({ url: `${iomsApi}/approval/inventory/queryApprovalControlById`, data }); // 根据审批ID查询审批详情
const inventoryRevokeApproval = data => postParamsQS({
  url: `${iomsApi}/approval/inventory/revokeApproval`, data
}); // 出入库流程撤销
const getConsumablesList = data => postRequest({ url: `${iomsApi}/iHCRSStatisticsController/getDepotManagementStockByWorkNum`, data }); // 出入库流程撤销
// 安防实验室
// 安全教育徽章数
const safeTatistics = data => post_json({ url: `${courseApi}/task/Count`, data });
// 公开课程
const openCourseList = data => post_json({ url: `${courseApi}/mine/online/list`, data });
const treeAllList = data => post_json({ url: `${courseApi}/subject/treeAll`, data });
const getCollect = data => server({ url: `${courseApi}/mine/collect`, data });
const videoTime = data => server({ url: `${courseApi}/mine/make`, data });
// 课后习题用户提交考试
const userAnswer = data => post_json({ url: `${courseApi}/mine/make/question/submit`, data });
// 我的课程
const myCourseList = data => post_json({ url: `${courseApi}/mine/list`, data });
// 我的课程（新）
const myCourseNewList = data => post_json({ url: `${courseApi}/mine/listNew`, data });
// 考试任务列表
const questionsList = data => post_json({ url: `${courseApi}/examinationPlan/selectTaskByPage`, data })
// 考试任务详情
const examitDetail = data => post_json({ url: `${courseApi}/examinationPlan/getRecordInfo`, data })
// 考试计划详情
const examitPlanDetail = data => post_json({ url: `${courseApi}/examinationPlan/getInfo`, data })
// 考试提交
const submitExam = data => post_json({ url: `${courseApi}/examinationPlan/exam`, data })
// 培训任务列表
const trainPlanList = data => post_json({ url: `${courseApi}trainPlan/selectTaskByPage`, data })
// 培训任务详情
const trainPlanDetail = data => post_json({ url: `${courseApi}/trainPlan/getTaskInfo`, data })
// 培训计划详情
const trainProjectDetail = data => post_json({ url: `${courseApi}/trainPlan/getInfo`, data })
// 培训签到
const trainSgin = data => post_json({ url: `${courseApi}/trainPlan/QRSignIn`, data })
// 培训签到上传
const trainUpload = data => post_json({ url: `${courseApi}/trainPlan/uploadMaterial`, data })
// 微信签名
const getSignature = data => post_json({ url: `${ipsmsApi}/config/getJsApiConfig`, data });
// 登录
const loginLaboratory = data => post_formData({ url: `${ipsmsApi}/userLoginController/userLoginByMobile`, data });
// 我的任务
const learnTaskList = data => post_json({ url: `${courseApi}/learnTask/selectMyTaskByPage`, data })
// 我的任务详情
const learnTaskDetail = data => post_json({ url: `${courseApi}/learnTask/getNyTaskInfo`, data })
// 根据位置获取科室列表
const getDeptListByLocation = data => postParamsQS({ url: `${iomsApi}/AppWorkOrderController/getDeptByLocalCode`, data })
// 根据科室获取地点列表
const getLocationListByDept = data => postParamsQS({ url: `${iomsApi}/AppWorkOrderController/getNewSpaceInfoByDeptId`, data })
// 获取人员信息
const getPersonnelInfo = data => getRequest({ url: `${spaceApi}/hospitalStaff/hospital-staff/getUpdateDetails`, data })
// 加签
const projectAddSignature = data => postRequest({ url: `${sApi}/assignmentInfo/assignmentAddSignature`, data })
// 获取触发配置
const getTriggerConfig = data => postRequest({ url: `${iomsApi}/trigger/config/getTriggerConfig`, data })
// 出库单列表
const getOutboundList = data => postParamsQS({ url: `${chemicalWebApi}/outwarehouseRecord/listData`, data })
// 出库单详情
const getOutboundDetail = data => postParamsQS({ url: `${chemicalWebApi}/outwarehouseRecord/view`, data })
// 字典管理
const getDictByPage = data => postParamsQS({ url: `${chemicalWebApi}/materialsDict/materialTypeTree`, data })
// 仓库列表
const getWarehouseList = data => postParamsQS({ url: `${chemicalWebApi}/warehouse/listData`, data })
// 仓库列表APP
const getWarehouseListForApp = data => postParamsQS({ url: `${chemicalWebApi}/inventoryManage/getWarehouseManageListForApp`, data })
// 工单领料耗材查询
const getInventoryListForApp = data => postParamsQS({ url: `${chemicalWebApi}/inventoryManage/getInventoryListForApp`, data })
// 根据出库单查询耗材
const getOutDetailList = data => postParamsQS({ url: `${chemicalWebApi}/outwarehouseRecord/getOutDetailList`, data })
// 出库单提交
const submitOutbound = data => postParamsQS({ url: `${chemicalWebApi}/outwarehouseRecord/save`, data })
// 入库单提交
const submitInbound = data => postParamsQS({ url: `${chemicalWebApi}/inwarehouseRecord/save`, data })
// 查询盘点列表
const getTakeStockByPage = data => postRequest({ url: `${chemicalWebApi}/takeStock/getTakeStockList`, data })
// 查询盘点详情
const getTakeStockById = data => postRequest({ url: `${chemicalWebApi}/takeStock/getTakeStockInfo`, data })
// 盘点数据
const checkTakeStockInfo = data => postRequest({ url: `${chemicalWebApi}/takeStock/checkStockSlave`, data })
//根据id查询盘点单耗材详情
const getStockSlaveById = data => getRequest({ url: `${chemicalWebApi}/takeStock/getStockSlaveById`, data })

export default {
  getWarehouseListForApp,
  submitInbound,
  submitOutbound,
  getOutDetailList,
  getInventoryListForApp,
  getWarehouseList,
  getDictByPage,
  getOutboundList,
  getOutboundDetail,
  projectAddSignature,
  getOutDepotNumberByWorkNum,
  getOutsourcingCompanyInfo,
  getWorkTypeListTeam,
  getServiceWorkers,
  querySurveyListByMenuCode,
  countMonitorPointByMenuCode,
  queryMenuGroup,
  querySurveyDetail,
  querySurveyCount,
  queryMenuByPage,
  getBoilerByProjectCode,
  energyStatisticsDetail,
  countWarnByTime,
  queryAlarmTrendFromTime,
  queryBoilerMonitorDetail,
  querySurveyListPageByMenuCode,
  getAssetOverviewPieChart,
  getElevatorAssetList,
  airGetEntityMenuList,
  getSurveyStatusByImhCode,
  getRealMonitoring,
  airViewDetailApp,
  airRunPolice,
  airViewDetail,
  airRunRate,
  airRunTime,
  getLightingBySpaceId,
  getLightingByGroupId,
  getSelectStatusList,
  selectByList,
  structureTree,
  groupOperationMonitoring,
  countOverview,
  getPoliceInfoByApp,
  getScadaList,
  getPoliceInfoByApp,
  getRealEnvMonitoringListApp,
  getPoliceCount,
  getChildMenuCodesByProjectCode,
  getWaterOverview,
  getColdHostStatistics,
  getEntityMenuList,
  getScadaList,
  getPoliceInfoByApp,
  getAlarmTrend,
  getAlarmSourceCount,
  getAlarmTopCount,
  getPolicePieByApp,
  selectAlarmRecordAll,
  getWaterSupplyTotal,
  getRealMonitoringList,
  selectClassicsAlarm,
  getWaterSupplyTotal,
  savaTeamMember,
  getGridTreeData,
  getNewStaffInfoById,
  urgeOderByWeChat,
  getUnfinishedMineTask,
  // saveTaskByWeChat,
  getOfficialCarDateById,
  getOfficialCarList,
  officeSignature,
  findWeightRecordListBySign,
  cascadingQueryHospitalGridInfo,
  getOpenid,
  userLoginIn,
  saveUserWechat,
  getStaffInfoByType,
  getHospitalOfficeInfo,
  getDictArray,
  getHospitalListAndLogoByUnitCode,
  userRegister,
  cascadingQueryAreaList,
  updateMyCenter,
  getOfficeAndTeamList,
  getItemTypeList,
  getPersonalCentre,
  getStayTaskOrderList,
  getTeamWorkInfo,
  getMineTask,
  getTaskDetail,
  appRollbackTask,
  getNotCommentWarn,
  getDepotCatalogueList,
  getDepotManagementList,
  updateStaffInfo,
  getEntryOrdersReason,
  saveScheduling,
  saveEntryOrders,
  saveComplete,
  toSaveEvaluate,
  getCancelReason,
  getLastThreeLocations,
  getPersonnelDictionary,
  getTeamsByTask,
  getTeamsByTaskAll,
  toTeamsChangeTask,
  toTeamsChangeTaskNew,
  getHospitalIsRisterByCode,
  getDesignatePersonList,
  getHospitalDispatchingConfig,
  getMineTaskPage,
  getMalfunctionReasonMethod,
  saveMalfunctionReasonMethod,
  updateTask,
  getTransportType,
  getOfficeWasteInfo,
  wasteRecordMonthlyReport,
  wasteRecordMonthlyReportDetail,
  getOpinionList,
  replyOpinion,
  getOpinionDetail,
  saveOpinion,
  getWorkTypeList,
  getNotice,
  getNoticeDetail,
  getNoticeList,
  getDisNoticeDetail,
  getCustomizeTaskConfiguration,
  getTaskWorkConfiguration,
  arrive,
  taskStatistics,
  todayTaskList,
  taskListByAnalysis,
  taskType,
  deviceType,
  taskList,
  getServiceOverview,
  competentList,
  deviceList,
  deviceDetails,
  deviceDetailsCopy,
  maintenanceList,
  taskDetail,
  taskBookDetail,
  uploadImg,
  getOrderCountByUserInfo,
  checkAndAccept,
  getDictionary,
  getItemList,
  getAssetName,
  checkAndAccept,
  getAccountList,
  inspectionSubmit,
  getPerformTask,
  taskRepair,
  taskAnalysis,
  getMessageListAppByCategory,
  approvalBacklog,
  finishToDoListLevel,
  getMedicalWasteType,
  getMedicalTopten,
  getMedicalWasteOption,
  taskComplete,
  taskAnalysisCharts,
  taskAnalysisList,
  getRepairArea,
  getMaintenanceCosts,
  getFixCostAnalysis,
  getConsumableNum,

  getDepartmentMaintenance,
  spaceList,
  getSuperiorData,
  spaceSelectByList,
  spacePageList,
  getDepartmentMaintenance,
  getWorkOrderList,
  getMenuAuth,

  inspectionType,
  selfQuestionListToWx,
  selfQuestionCount,
  getPaperQuestions,
  saveAnswer,

  inspectionType,
  subDeviceRepair,
  saveOperateRecord,
  inquireRepair,
  inspectionScanCode,
  GetSettingOverview,
  GetSettingDetails,
  GetColdOverview,

  linenSumType,
  linenStatus,
  linenRanking,
  linenAgeing,
  KgceLogin,
  GetEnergyAndClassifyTree,
  GetModelEnergyDataList,
  executeTesk,
  GetAirStatistics,
  getOlgTransportItem,
  GetParentMenuList,
  GetEnvironmentMonitor,
  GetEquipmentOperationMonitor,
  GetKeyDeviceCount,
  GetBoilerMonitorDetail,
  GetRealMonitoringListSecurity,
  GetDeviceOperating,
  GetSpaceApplyList,
  AddSpaceApply,
  GetDictionaryList,
  GetSpaceApplyDetails,
  DeleteApplyById,
  UpdateSpaceApply,
  GetEnergyStatistics,
  GetBoilerMonitorList,
  GetGasMonitoring,
  GetBoilerCount,
  GetAirRunPoliceDetail,
  GetSpaceCheckList,
  GetDeptTaskDetails,
  GetDeptSpaceDetails,
  AddRegistSpaceInfo,
  GetRegistSpaceInfo,
  GetSpaceNum,
  DeleteRegistInfoById,
  UpdateSpaceStatus,

  getOperateTotalDetail,
  getParkingSuperviseInfo,
  getExistChargeDetail,

  // 抄表管理相关接口
  dormDialList,
  dialInfo,
  userList,
  addDial,
  dialRecordsList,
  // 床位人员信息

  bedUserInfo,

  isConfirmOrder,

  confirmOrder,
  getAllOffice,
  // 工程管理
  examineApproveList,
  checkUserExists,
  getApprovalByType,
  getDict,
  getAppr,
  getContract,
  getByProjectId,
  getListByProject,
  getListByProjectId,
  getProjectList,
  getProjecDetails,
  getStatements,
  getProjectListBy,
  getProjectListById,
  getByProjectList,
  getlistByProjectId,
  getRectification,
  getPrintRectification,
  getCountByProject,
  getIntermedList,
  addContractMark,
  addIntermediate,
  getNoList,
  getDepartmentList,
  getDetailsContract,
  getcontractlist,
  getSearch,
  getPaymentNodeTaskListByProjectId,
  getEditSave,
  getQualityInspectionAdd,
  getScceptanceItems,
  getEditSaves,
  getApprSave,
  getContractSubmit,
  getAcceptance,
  getAcceptanceEditSave,
  getAcceptanceDelete,
  getDisposeContract,
  getNewSysConfigParam,
  getQueryTaskData,
  getTaskDataById,
  updateTaskData,
  staffList,
  updateTaskNoticeData,
  sureTaskNoticeData,
  GetPlanList,
  GetPlanDetail,
  //结束
  voiceId2url,
  taskPointAmountStatistics,
  fromPointList,
  pageListByBedId,
  zdyPointDetail,
  getServiceStatusData,
  getServiceInfoData,
  getEquipmentTypeStatisticsData,
  getEquipmentStatusStatisticsData,
  submitScanFile,
  checkScanFile,
  lookUpById,
  //报警中心
  queryAllAlarmStatisticsCount,
  selectAlarmRecordById,
  queryLimAlarmFile,
  getShield,
  insertRemarkById,
  handleAlarmAffirmById,
  OneKeyDispatch,
  setAlarmRecordToClassics,
  getSelected,
  getSelectedDept,
  summarySave,
  getAlarmThirdSystemList,
  queryFieldsConfigList,
  getInspectionTaskVoPage,
  //物料
  findDepotManagementStockPage,
  outDepotManagementStock,
  findDepotManagementRecordPage,
  checkIsTimeout,
  // 空间设施登记
  spaceDeviceList,
  getFacilityDetails,
  otherEquList,
  changeViewStatus,
  saveRegistration,
  getDeviceCountGroupedByFacilityType,
  changeRecord,
  submitRecord,
  submitRecordDetail,
  getSpaceFacilityConfig,
  queryFacilityDeviceTreeByApp,
  queryFacilityDeviceByApp,
  saveDeviceBuffer,
  // 协和一站式
  queryApprovalList,
  saveApprovalControl,
  queryDetailList,
  approveOrReject,
  revokeApproval,
  confirmArrival,
  queryArrival,
  // 协和一站式第三版
  saveInventoryApprovalControl,
  queryMyApprovalList,
  queryApprovalControlById,
  inventoryRevokeApproval,
  getConsumablesList,
  checkIsTimeout,
  myCourseList,
  myCourseNewList,
  questionsList,
  examitDetail,
  examitPlanDetail,
  submitExam,
  trainPlanList,
  trainPlanDetail,
  trainProjectDetail,
  trainSgin,
  trainUpload,
  openCourseList,
  safeTatistics,
  treeAllList,
  getCollect,
  videoTime,
  userAnswer,
  getSignature,
  learnTaskList,
  learnTaskDetail,
  loginLaboratory,
  getDeptListByLocation,
  getLocationListByDept,
  getPersonnelInfo,
  ...constructionOperation,
  getTriggerConfig,
  getTakeStockByPage,
  getTakeStockById,
  checkTakeStockInfo,
  getStockSlaveById
};
