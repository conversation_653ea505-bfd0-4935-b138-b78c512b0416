<template>
  <div class="container">
    <Header title="工单操作" @backFun="goBack"></Header>
    <!-- 未派工选项卡进入 抢单或派工处理 -->
    <div class="vie-info" v-if="isRobOrAppoint">
      <!-- 选择指派人员信息 -->
      <div class="item notice-wrapper border-bottoom designate-css">
        <div class="weui-cell__hd">
          <label class="weui-label">指派人员</label>
        </div>
        <div class="weui-cell__bd text-wrapper" @click="handleDesignatePersonnel()" :class="{ disClick: isVieOder, disabledClick: disabled }">
          <span v-if="personnelVal && workOrdeInfoDate">{{ designatePersonnel }}</span>
          <span v-else style="display: inline-block; width: 100%">
            请选择
            <span class="iconfont" style="float: right">&#xe646;</span>
          </span>
        </div>
      </div>
      <div class="border-bottoom item notice-wrapper" v-if="!isVieOder">
        <div class="weui-cell__hd">
          <label class="weui-label">消息通知</label>
        </div>
        <div class="weui-cell__bd text-wrapper">
          <div class="line-css" v-for="(item, i) of designateList" :key="i">
            {{ item }}
          </div>
        </div>
        <div class="weui-cell__ft"></div>
      </div>
    </div>

    <!-- 已派工选项卡进入 派工处理 -->
    <add-personnel
      ref="addPersonnel"
      source="pending"
      v-if="source == 'pending'"
      :isShow="isShow"
      :isVieOder="isVieOder"
      :disabled="disabled"
      :workOrdeInfoDate="workOrdeInfoDate"
      :designatePersonnelTitle="designatePersonnelTitle"
      :workOrderId="workOrderId"
    ></add-personnel>

    <!--转派处理-->
    <change-order-handle
      v-if="handleChangeOderInfo"
      :title="handleComponentsTitle"
      :workTypeCode="workTypeCode"
      :localtionId="localtionId"
      :itemTypeCode="itemTypeCode"
      :workOrdeInfoDate="workOrdeInfoDate"
      :designateDeptCode="$route.query.designateDeptCode"
      ref="changeOrder"
    ></change-order-handle>

    <!-- 挂单和完工 -->
    <div v-if="inputInfo">
      <!-- 挂单处理 -->
      <pending-order v-if="isPendingOder" ref="pendingOder" :time="timeFunc(Date.parse(new Date()), '.')" @changeVal="handleChildChangeVal"></pending-order>

      <!-- 完工处理 -->
      <completed-oder
        v-if="!isPendingOder"
        ref="finished"
        :workTypeCode="workTypeCode"
        :inputInfo="inputInfo"
        :showCompletedConList="showCompletedConList"
        :isPendingOder="isPendingOder"
        :isConfirm="isConfirm"
        :isHadDesP="isHadDesP"
        :localtionId="getLocationId()"
        :workOrderId="workOrderId"
        :workOrdeInfoDate="workOrdeInfoDate"
        :selectedComConsumables="selectedComConsumables"
        :time="timeFunc(Date.parse(new Date()), '.')"
        :completePriceFlag="this.$route.query.completePriceFlag"
        :isAutoConsumables="isAutoConsumables"
      ></completed-oder>
    </div>

    <!-- 根据抢单、派工、挂单、完工操作显示不同按钮内容 -->
    <div class="btns" v-show="!($refs.finished && $refs.finished.isShowSign)">
      <button class="weui-btn weui-btn_primary btn" @click="handleSubmitVieOderClick" :class="{ 'weui-btn_disabled': isDis }">
        <span v-if="isVieOder">{{ isSure }}</span>
        <span v-else>{{ isAppoint }}</span>
      </button>
    </div>
  </div>
</template>

<script>
import RobOrAppointOrder from "@/pages/mine/components/RobOrAppointOrder";
import changeOrderHandle from "@/pages/mine/components/ChangeOrderHandle";
import AddPersonnel from "./components/AddDesignatePersonnel";
import PendingOrder from "./components/PendingOrder";
import CompletedOder from "./components/CompletedOder";
import global from "@/utils/Global.js";
import { mapState } from "vuex";
import fontSizeMixin from "@/mixins/fontSizeMixin";
export default {
  mixins: [fontSizeMixin],
  name: "OrderHandle",
  components: {
    RobOrAppointOrder,
    changeOrderHandle,
    AddPersonnel,
    PendingOrder,
    CompletedOder
  },
  data() {
    return {
      isRobOrAppoint: false, // 未派工选项卡的抢单或派工操作
      spaceId: "",
      isDis: false,
      disab: false,
      disabled: false,
      isSure: "确定",
      isAppoint: "派工",
      designatePersonnelTitle: "抢单",
      isShow: true, //从推送消息进入后的指派人员等相关信息是否显示 true 不显示
      isVieOder: true, //是否显示消息通知（为派工工单） true只显示抢单同时不显示消息通知
      personnelVal: "",
      workOrdeInfoDate: [],
      designatePersonnel: "",
      noticePeople: "派工人员",
      designatePersonName: "",
      designatePersonnelTel: "",
      designatePersonnelOpenId: "",
      designatePersonnelId: "",
      workOrderId: "",
      // robParams: this.$route.query.robParams || {}, // 抢单接口参数
      selectedCheckboxs: [],
      handleChangeOderInfo: false, //显示转派操作
      handleComponentsTitle: "", // 转派操作标题
      workTypeCode: "",
      localtionId: "",
      itemTypeCode: "",

      source: "", // 已派工选项卡进入
      isDispatching: "",

      isPendingOder: false, // 挂单操作组件

      inputInfo: false,
      showCompletedConList: false, //完工
      isConfirm: true,
      selectedComConsumables: [],
      completePrice: "", //总服务费
      reason: [], //故障原因和维修方法
      isHadDesP: false,
      designateList: {}, //消息通知列表  仅做展示用
      isAutoConsumables: false,
      originalConsumables: []
    };
  },
  computed: {
    ...mapState(["loginInfo", "staffInfo", "addDesignatePersonnel"])
  },
  methods: {
    goBack() {
      if (this.$refs.finished && this.$refs.finished.isShowSign) {
        this.$refs.finished.isShowSign = false;
        this.$refs.finished.evaluationAdvice = "";
        return;
      }
      this.$router.go(-1);
    },
    saveAutoConsumablesState() {
      sessionStorage.setItem('autoConsumablesState', JSON.stringify({
        isAutoConsumables: this.isAutoConsumables,
        originalConsumables: this.originalConsumables,
        selectedComConsumables: this.selectedComConsumables
      }));
    },
    restoreAutoConsumablesState() {
      const state = sessionStorage.getItem('autoConsumablesState');
      if (state) {
        const { isAutoConsumables, originalConsumables, selectedComConsumables } = JSON.parse(state);
        this.isAutoConsumables = isAutoConsumables;
        this.originalConsumables = originalConsumables;
        this.selectedComConsumables = selectedComConsumables;
      }
    },
    getConfig() {
      this.$api.getNewSysConfigParam({}).then(res => {
        if (res.olgWorkPushNew.completionMaterialOutStockLinkConfig == 1) {
          this.isAutoConsumables = true;
          this.getConsumablesList();
          // this.saveAutoConsumablesState();
        }
      });
    },
    getConsumablesList() {
      this.$api
        .getConsumablesList({
          workNum: this.workOrdeInfoDate[0].workNum
        })
        .then(res => {
          // console.log("耗材", res);
          this.originalConsumables = res;
          this.selectedComConsumables = res.map(item => ({
            price: item.price,
            specification: item.specification,
            num: item.changeNum,
            depThreeTypeId: item.depotId,
            depThreeTypeName: item.depProductName
          }));
        });
    },
    getLocationId() {
      return this.workOrdeInfoDate && this.workOrdeInfoDate[0] ? this.workOrdeInfoDate[0].localtionId : "";
    },
    /**
     * 时间格式化载体
     */
    timeFunc() {},
    /**
     * 跳转到指派人员页面
     */
    handleDesignatePersonnel() {
      this.$router.push({
        path: "/personnel",
        query: {
          isShow: this.isShow,
          isVieOder: this.isVieOder,
          personnelVal: this.personnelVal,
          noticePeople: this.noticePeople,
          disabled: this.disabled,
          designateDeptCode: this.workOrdeInfoDate[0].designateDeptCode,
          workOrdeInfoDate: this.workOrdeInfoDate,
          designatePersonnelTitle: this.designatePersonnelTitle,
          workOrderId: this.workOrderId,
          isRobOrAppoint: this.isRobOrAppoint
        }
      });
    },
    /**
     * 抢单或派工成功请求
     */
    handleSubmitVieOderClick() {
      // console.log(this.designatePersonnelId);
      this.workOrdeInfoDate = this.workOrdeInfoDate ? this.workOrdeInfoDate : [];
      let res = this.workOrdeInfoDate[0];
      // console.log(res, "======");
      // this.spaceId = res.localtionId;
      if (this.handleChangeOderInfo) {
        //转派请求
        if (!this.$refs.changeOrder.teamInfo.name) {
          $.toast("请选择服务部门", "text");
          return;
        }
        // if (this.$refs.changeOrder.teamOldCode == this.$refs.changeOrder.teamNewCode) {
        //   if (!this.$refs.changeOrder.designatePersonnel) {
        //     $.toast("请选择服务人员", "text");
        //     return;
        //   }
        // }
        // if (!this.$refs.changeOrder.textareaValue) {
        //   $.toast("请填写转派说明", "text");
        //   return;
        // }
        let that = this;
        this.$api
          .toTeamsChangeTaskNew({
            taskId: this.workOrderId,
            userId: this.loginInfo.id,
            userName: this.loginInfo.staffName,
            designateDeptCode: this.$refs.changeOrder.teamInfo.id,
            designateDeptName: this.$refs.changeOrder.teamInfo.name,
            realName: this.loginInfo.staffName,
            appId: process.env.WxAppid,
            feedbackExplain: this.$refs.changeOrder.textareaValue, //转派说明
            deptCode: this.loginInfo.deptId, //当前用户所属班组code
            designatePersonPhone: this.designatePersonnelTel,
            designatePersonCode: this.designatePersonnelId,
            designatePersonName: this.designatePersonnel
            // openId: this.designatePersonnelOpenId
          })
          .then(res => {
            $.toast("转派成功", function () {
              // window.location.href = process.env.WX + "/personalCenter";
              // that.$router.go(-2);
              that.$router.push({
                path: "/workbench",
                query: {
                  type: "3",
                  leaguerType: "1"
                }
              });
            });
          })
          .catch(res => {
            if (res.data.code == "500") {
              this.$api
                .toTeamsChangeTaskNew({
                  taskId: this.workOrderId,
                  userId: this.loginInfo.id,
                  userName: this.loginInfo.staffName,
                  designateDeptCode: this.$refs.changeOrder.teamInfo.id,
                  designateDeptName: this.$refs.changeOrder.teamInfo.name,
                  realName: this.loginInfo.staffName,
                  appId: process.env.WxAppid,
                  feedbackExplain: this.$refs.changeOrder.textareaValue, //转派说明
                  deptCode: this.loginInfo.deptId, //当前用户所属班组code
                  designatePersonPhone: this.designatePersonnelTel,
                  designatePersonCode: this.designatePersonnelId,
                  designatePersonName: this.designatePersonnel
                  // openId: this.designatePersonnelOpenId
                })
                .then(res => {
                  $.toast("转派成功", function () {
                    // window.location.href = process.env.WX + "/personalCenter";
                    // that.$router.go(-2);
                    that.$router.push({
                      path: "/workbench",
                      query: {
                        type: "3",
                        leaguerType: "1"
                      }
                    });
                  });
                });
            }
          });
      } else if (this.isPendOder) {
        // console.log("我的工作台");
        let pendingReasonVal = this.$refs.pendingOder.pendingVal;
        let pendingSolutionVal = this.$refs.pendingOder.value;
        let pendingSolutionTime = this.$refs.pendingOder.ResolutionTime;

        //我的工作台-未处理
        this.$api
          .saveEntryOrders({
            id: this.workOrderId,
            userId: this.loginInfo.id,
            disEntryOrdersReason: pendingReasonVal,
            disEntryOrdersSolution: pendingSolutionVal,
            disPlanSolutionTime: pendingSolutionTime
          })
          .then(this.handleSubmitVieOderSucc);
      } else if ((this.source == "pending" || this.isRobOrAppoint) && this.designatePersonnelId) {
        if (this.source == "pending") {
          // 从已派工选项卡进入
          console.log("已派工派单");

          this.$api
            .saveScheduling({
              id: this.workOrderId,
              userId: this.loginInfo.id,
              realName: this.loginInfo.staffName,
              designatePersonCode: this.designatePersonnelId,
              designatePersonName: this.designatePersonnel,
              designatePersonPhone: this.designatePersonnelTel,
              // appId: process.env.WxAppid, //用于区分公众号推送
              // openId: this.designatePersonnelOpenId,
              staffId: this.staffInfo.staffId,
              reAssignment: this.isDispatching == "0" ? "1" : "2"
            })
            .then(this.handleSubmitVieOderSucc);
        } else {
          // 未派工选项卡进入
          console.log("未派工派单");
          // 增加loading
          $.showLoading();
          //抢单或派工
          this.$api
            .saveScheduling({
              id: this.workOrderId,
              userId: this.loginInfo.id,
              realName: this.loginInfo.staffName,
              designatePersonCode: this.designatePersonnelId,
              designatePersonName: this.designatePersonnel,
              designatePersonPhone: this.designatePersonnelTel,
              // appId: this.staffInfo.appList[0].appId,
              // openId: this.designatePersonnelOpenId,
              staffId: this.staffInfo.staffId
            })
            .then(this.handleSubmitVieOderSucc)
            .catch(err => {
              $.hideLoading(); // 添加错误处理时的loading隐藏
              console.log('err', err)
              $.toast(err.message || err.data.message || "操作失败", "text");
            });
        }
      } else if (this.inputInfo && this.isPendingOder) {
        console.log("挂单");
        //挂单
        this.pendingReasonVal = this.$refs.pendingOder.pendingVal;
        this.disEntryOrdersReasonCode = this.$refs.pendingOder.disEntryOrdersReasonCode;
        this.pendingSolutionVal = this.$refs.pendingOder.value;
        let pendingSolutionTime = this.$refs.pendingOder.ResolutionTime;
        if (!this.pendingReasonVal) {
          $.toast("请选择挂单说明", "text");
          return;
        }
        if (this.$refs.pendingOder.pendingApprovalConfig == 1) {
          //开启挂单审批
          let params = {
            businessJson: JSON.stringify({
              disEntryOrdersReason: this.pendingReasonVal,
              disEntryOrdersReasonCode: this.disEntryOrdersReasonCode,
              disEntryOrdersSolution: this.pendingSolutionVal,
              disPlanSolutionTime: pendingSolutionTime
            }),
            businessId: res.workNum,
            userId: this.loginInfo.staffId, //当前用户id
            userName: this.loginInfo.staffName //当前用户id
          };
          this.$api
            .saveApprovalControl(params)
            .then(this.handleSucc)
            .catch(err => {
              $.toast(err.data.message, "text");
            });
        } else {
          this.$api
            .saveEntryOrders({
              id: this.workOrderId, //调度工单id
              userId: this.loginInfo.id, //当前用户id
              disEntryOrdersReason: this.pendingReasonVal,
              disEntryOrdersReasonCode: this.disEntryOrdersReasonCode,
              disEntryOrdersSolution: this.pendingSolutionVal,
              disPlanSolutionTime: pendingSolutionTime,
              realName: this.staffInfo.staffName,
              deptCode: this.staffInfo.teamId,
              deptName: this.staffInfo.teamName
            })
            .then(this.handleSucc)
            .catch(err => {
              $.toast(err.data.message, "text");
            });
        }
      } else if (this.inputInfo && !this.isPendingOder) {
        console.log("完工");
        //完工
        let personCode = "";
        let personName = "";
        let personTel = "";
        let perInfo = this.$refs.finished.$refs.addPersonnel;
        if (!this.workOrdeInfoDate[0].designatePersonCode && !perInfo.designatePersonnelId) {
          $.toast("请选择指派人员", "text");
          return false;
        } else if (!this.workOrdeInfoDate[0].designatePersonCode && perInfo.designatePersonnelId) {
          //判断是否为一体机自动接单的单子来确定指派人员信息的字段
          personCode = perInfo.designatePersonnelId;
          personName = perInfo.designatePersonnel;
          personTel = perInfo.designatePersonnelTel;
        } else {
          personCode = this.workOrdeInfoDate[0].designatePersonCode;
          personName = this.workOrdeInfoDate[0].designatePersonName;
          personTel = this.workOrdeInfoDate[0].designatePersonnelTel;
        }
        let starNum = this.$refs.finished.starNum;
        this.imgSrc = this.$refs.finished.imgSrc;
        this.completePrice = this.$refs.finished.completePrice;
        this.reason = this.$refs.finished.reason;
        let val = this.$refs.finished.value;
        let facilityType = this.$refs.finished.facilityType;
        let facilityName = this.$refs.finished.facilityName;
        let timestamp = new Date().getTime();
        let time = this.timestampToTime(timestamp);
        // 判断耗材数量是否为空
        let selectedComConsumablesNum = Boolean;
        this.selectedComConsumables.forEach(v => {
          if (v.num == "") {
            return (selectedComConsumablesNum = false);
          }
        });
        if (!selectedComConsumablesNum && !this.isAutoConsumables) {
          $.toast("耗材数量不能为空", "text");
          return false;
        }
        // if (!val) {
        //   $.toast("请填写完工说明", "text");
        //   return false;
        // }
        console.log("图片数量", this.$refs.finished.$refs.imgs.fileList.length);
        console.log("已上传图片数量", this.$refs.finished.imagesParams.length);
        if (this.$refs.finished.$refs.imgs.fileList.length > 0 && this.$refs.finished.$refs.imgs.fileList.length != this.$refs.finished.imagesParams.length) {
          $.toast("图片未上传完成", "text");
          return false;
        }
        if (this.$refs.finished.completeUploadImage && this.$refs.finished.imagesParams.length == 0) {
          $.toast("请上传图片", "text");
          return false;
        }

        // 准备提交的耗材数据
        let submitConsumables = this.selectedComConsumables;

        // 只在自动耗材模式下进行校验
        if (this.isAutoConsumables) {
          submitConsumables = [];
          for (let i = 0; i < this.selectedComConsumables.length; i++) {
            const current = this.selectedComConsumables[i];
            // 跳过数量为0的耗材
            if (!current.num || current.num == 0) {
              continue;
            }
            
            // 查找对应的原始耗材记录
            const original = this.originalConsumables.find(item => 
              item.depotId === current.depThreeTypeId
            );
            
            if (original) {
              // 检查数量是否超出限制
              if (Number(current.num) > Number(original.changeNum)) {
                $.toast(`${current.depThreeTypeName}数量不能超过${original.changeNum}`, "text");
                return false;
              }
              submitConsumables.push({
                depThreeTypeName: current.depThreeTypeName,
                depThreeTypeId: current.depThreeTypeId,
                num: current.num,
                price: current.price,
                specification: current.specification
              });
            }
          }
        }

        // 使用处理后的耗材列表进行提交
        this.$api
          .saveComplete({
            id: this.workOrderId, //调度工单id
            workNum: res.workNum, //工单号
            userId: this.loginInfo.id, //当前用户id
            workSources: res.workSources, //工单来源
            designatePersonCode: personCode, //指派人员code
            designatePersonName: personName, //指派人员名字
            designatePersonPhone: personTel, //指派人员电话
            sourcesAddressCode: res.sourcesAddressCode, //(申报人)所在楼层code
            sourcesDeptName: res.sourcesDeptName, //使用(申报人)科室
            sourcesDeptCode: res.sourcesDeptCode, //使用(申报人)科室code
            actual: JSON.stringify(submitConsumables), //实际耗材消耗json[]
            disDegree: starNum, //满意度
            disFinishTime: time, //完工时间(yyyy-MM-ddHH:mm:ss)
            disFinishRemark: val, //备注说明
            imgBase: this.imgSrc, //签名
            appId: this.staffInfo.appList ? this.staffInfo.appList[0].appId : "", //用于区分公众号推送
            staffId: this.staffInfo.staffId,
            completePrice: this.completePrice, //总服务费
            reason: JSON.stringify(this.reason),
            newAttachmentUrl: this.$refs.finished.imagesParams.join(","), //图片附件
            evaluationAdvice: this.$refs.finished.evaluationAdvice,
            evaluationExplain: this.$refs.finished.evaluationExplain,
            facilityType: facilityType, //设备id
            facilityName: facilityName, //设备name
            staffName: this.staffInfo.staffName,
          })
          .then(this.handleSucc);
      } else {
        $.hideLoading();
        $.toast("请选择派工人员", "text");
      }
    },
    /**
     * 抢单或派工成功成功回调函数
     */
    handleSubmitVieOderSucc(res) {
      $.hideLoading();
      if (this.personnelVal == false) {
        $.toast("请选择指派人员", "text");
      } else {
        this.isDis = true;
        this.disabled = true;
        $.toast(res.flowtype);
        setTimeout(() => {
          console.log("派工跳转了");
          // this.$router.go(-2);
          this.$router.push({ path: "workbench?type=3&workTypeCode=&leaguerType=1" });
        }, 1000);
      }
    },

    /**
     * 从指派人员页面返回时获取人员信息
     */
    appointPersonnel() {
      //如果有指派人员就展示，没有就展示请选择
      if (this.addDesignatePersonnel.length > 0 && this.$route.query.backParams) {
        this.workOrdeInfoDate = this.$route.query.backParams.workOrdeInfoDate;
        this.isShow = this.$route.query.backParams.isShow;
        this.disabled = this.$route.query.backParams.disabled;
        this.designatePersonnelTitle = this.$route.query.backParams.designatePersonnelTitle;
        this.workOrderId = this.$route.query.backParams.workOrderId;
        this.isVieOder = this.$route.query.backParams.isVieOder == "true" ? true : false;
        this.isRobOrAppoint = this.$route.query.backParams.isRobOrAppoint == "true" ? true : false;
        //派单处理
        this.handleChangeOderInfo = this.$route.query.backParams.handleChangeOderInfo;
        this.handleComponentsTitle = this.$route.query.backParams.handleComponentsTitle;
        this.workTypeCode = this.$route.query.backParams.workTypeCode;
        this.localtionId = this.$route.query.backParams.localtionId;
        this.itemTypeCode = this.$route.query.backParams.itemTypeCode;

        this.source = this.$route.query.backParams.source;

        this.personnelVal = true;
        let addDesignatePers = this.addDesignatePersonnel;
        let allAtten = "";
        let allAttenId = "";
        let allAttenNotice = "";
        let allAttenTel = "";
        let allAttenOpenId = "";
        for (let i = 0; i < addDesignatePers.length; i++) {
          allAtten += addDesignatePers[i].designatePersonName + ",";
          allAttenId += addDesignatePers[i].designatePersonCode + ",";
          allAttenTel += addDesignatePers[i].designatePersonPhone + ",";
          allAttenOpenId += addDesignatePers[i].openId + ",";
          allAttenNotice += addDesignatePers[i].designatePersonName + addDesignatePers[i].designatePersonPhone + ",";
        }
        if (allAttenId == "") {
          this.personnelVal = false;
        } else {
          this.personnelVal = true;
        }
        this.designatePersonnel = allAtten.slice(0, allAtten.length - 1);
        this.designatePersonnelId = allAttenId.slice(0, allAttenId.length - 1);
        this.designatePersonnelTel = allAttenTel.slice(0, allAttenTel.length - 1);
        this.designatePersonnelOpenId = allAttenOpenId.slice(0, allAttenOpenId.length - 1);
        this.noticePeople = allAttenNotice.slice(0, allAttenNotice.length - 1);
        this.designateList = this.noticePeople.split(","); //消息通知 仅做展示用
      }
    },
    /**
     * 挂单的val值监测（按钮显示）
     * @param par
     */
    handleChildChangeVal(par) {
      if (par) {
        this.isDis = false;
        this.disab = false;
      } else {
        this.isDis = true;
        this.disab = true;
      }
    },
    /**
     * 成功提交数据
     */
    handleSucc(res) {
      let isMy = this.$route.query.isMy;
      $.toast("工单提交成功", () => {
        this.$router.push({
          path: "/workbench",
          query: {
            type: "3",
            leaguerType: "1",
            isMy: isMy
          }
        });
        sessionStorage.removeItem("taskDetailInfo");
        sessionStorage.removeItem("hasBeenObtainedTaskDetail");
        sessionStorage.removeItem("putConsumables");
        sessionStorage.removeItem("parameterObj");
      });
    },
    /**
     * 时间格式换算
     * @param timestamp 时间戳
     * @returns {string}
     */
    timestampToTime(timestamp) {
      let date = new Date(timestamp); //如果date为10位不需要乘1000
      let Y = date.getFullYear() + "-";
      let M = (date.getMonth() + 1 < 10 ? "0" + (date.getMonth() + 1) : date.getMonth() + 1) + "-";
      let D = (date.getDate() < 10 ? "0" + date.getDate() : date.getDate()) + " ";
      return Y + M + D;
    },
    /**
     * 耗材选择完成后回显
     */
    showConsumables () {
      if (this.$route.params.style == "completedOder") {
        //        从耗材选择进入
        this.selectedComConsumables = this.$route.params.comLists;
        let preArrayCom = this.selectedComConsumables;
        let arr = [];
        for (let i = 0; i < preArrayCom.length; i++) {
          arr[i] = {
            depThreeTypeName: preArrayCom[i].depThreeTypeName,
            depThreeTypeId: preArrayCom[i].depThreeTypeId,
            num: preArrayCom[i].num,
            price: preArrayCom[i].price,
            specification: preArrayCom[i].specification
          };
        } 
        this.selectedComConsumables = arr;
        this.selectedConsumables = this.$route.params.comLists;
        if (this.selectedComConsumables.length > 0) {
          this.showCompletedConList = true;
             if (this.$refs.finished) {
              this.$refs.finished.showCompletedCon = true;
            }
        }
        this.inputInfo = this.$route.params.inputInfo;
        this.showConList = this.$route.params.showConList;
        this.isPendingOder = this.$route.params.isPendingOder;
        this.isConfirm = this.$route.params.isConfirm;
        // this.saveAutoConsumablesState()
      }
    }
  },
  mounted () {
    this.$YBS.apiCloudEventKeyBack(this.goBack);
    this.appointPersonnel();
    this.getConfig();
    this.showConsumables()
    // window.addEventListener(
    //   "popstate",
    //   function(e) {
    //     sessionStorage.removeItem("parameterObj");
    //   },
    //   false
    // );
  },
  activated () {
    this.showConsumables();
    // this.restoreAutoConsumablesState();
  },
  beforeRouteEnter(to, from, next) {
    next(vm => {
      console.log('from.path',from.path);
      if (from.path == "/vieform") {
        if (to.query.isRobOrAppoint) {
          vm.isRobOrAppoint = JSON.parse(to.query.isRobOrAppoint);
          vm.workOrderId = to.query.workOrderId;
          vm.personnelVal = JSON.parse(to.query.personnelVal);
          vm.workOrdeInfoDate = to.query.workOrdeInfoDate;
          vm.isVieOder = JSON.parse(to.query.isVieOder);
          if (to.query.personnelVal) {
            // 抢单
            // console.log('抢单');
            vm.designatePersonnel = vm.staffInfo.staffName;
            vm.designatePersonnelId = vm.staffInfo.teamPersonId;
            vm.designatePersonnelTel = vm.staffInfo.mobile;
            vm.designatePersonnelOpenId = vm.staffInfo.wechat;
            vm.$route.meta.title = "抢单";
          } else if (!to.query.personnelVal) {
            // 派单
            // console.log('派单');
            vm.designatePersonnelTitle = to.query.designatePersonnelTitle;
          }
        } else if (to.query.handleChangeOderInfo) {
          // 转派
          // console.log('转派');
          vm.workOrderId = to.query.workOrderId;
          vm.handleChangeOderInfo = to.query.handleChangeOderInfo;
          vm.handleComponentsTitle = to.query.handleComponentsTitle;
          vm.workTypeCode = to.query.workTypeCode;
          vm.localtionId = to.query.localtionId;
          vm.itemTypeCode = to.query.itemTypeCode;
        }
      }
      if (from.path == "/completed" || from.path == "/Completed" || from.path == "/optionsTrouble" || from.path == "/consumables" || from.path == "/renewal") {
        // 从已派工或已挂单页面进入
        let parameterObj = JSON.parse(sessionStorage.getItem("parameterObj"));
        for (let item in parameterObj) {
          to.query[item] = parameterObj[item];
        }
        vm.source = to.query.source;
        vm.workOrdeInfoDate = to.query.workOrdeInfoDate;
        vm.workOrderId = to.query.workOrderId;
        // 转派
        vm.handleChangeOderInfo = to.query.handleChangeOderInfo;
        vm.handleComponentsTitle = to.query.handleComponentsTitle;
        vm.workTypeCode = to.query.workTypeCode;
        vm.localtionId = to.query.localtionId;
        vm.itemTypeCode = to.query.itemTypeCode;
        // 挂单
        if (to.query.isPendingOder) {
          vm.timeFunc = global.timestampToTime;
          vm.isPendingOder = to.query.isPendingOder;
          vm.inputInfo = to.query.inputInfo;
        } else if (to.query.isPendingOder == false) {
          // 完工
          vm.timeFunc = global.timestampToTime;
          vm.isPendingOder = to.query.isPendingOder;
          vm.inputInfo = to.query.inputInfo;
          vm.showCompletedConList = to.query.showCompletedConList;
          vm.workTypeCode = to.query.workTypeCode;
          vm.workOrderId = to.query.workOrderId;
          vm.isConfirm = to.query.isConfirm;
          vm.selectedComConsumables = to.query.selectedComConsumables;
          vm.workOrdeInfoDate = to.query.workOrdeInfoDate;
        }
        // if (vm.$route.params.style == "completedOder") {
        //   console.log(555555555555555555);
          
        //   //        从耗材选择进入
        //   vm.selectedComConsumables = vm.$route.params.comLists;
        //   let preArrayCom = vm.selectedComConsumables;
        //   let arr = [];
        //   for (let i = 0; i < preArrayCom.length; i++) {
        //     arr[i] = {
        //       depThreeTypeName: preArrayCom[i].depThreeTypeName,
        //       depThreeTypeId: preArrayCom[i].depThreeTypeId,
        //       num: preArrayCom[i].num,
        //       price: preArrayCom[i].price,
        //       specification: preArrayCom[i].specification
        //     };
        //   }
        //   vm.selectedComConsumables = arr;
        //   vm.selectedConsumables = vm.$route.params.comLists;
        //   if (vm.selectedComConsumables.length > 0) {
        //     vm.showCompletedConList = true;
        //     if (vm.$refs.finished) {
        //       vm.$refs.finished.showCompletedCon = true;
        //     }
        //   }
        //   vm.inputInfo = vm.$route.params.inputInfo;
        //   vm.showConList = vm.$route.params.showConList;
        //   vm.isPendingOder = vm.$route.params.isPendingOder;
        //   vm.isConfirm = vm.$route.params.isConfirm;
        // }
      }
      if (from.path == "/personnel") {
        if (!to.query.backParams || !to.query.backParams.workOrdeInfoDate) {
          // 未选择人员 或者 选择过人员最后一次未选择时物理键返回此页面
          console.log("未选择人员");
          vm.workOrdeInfoDate = from.query.workOrdeInfoDate;
          vm.isShow = from.query.isShow;
          vm.disabled = from.query.disabled;
          vm.designatePersonnelTitle = from.query.designatePersonnelTitle;
          vm.noticePeople = from.query.noticePeople;
          vm.isVieOder = from.query.isVieOder == "true" ? true : false;
          vm.personnelVal = from.query.personnelVal == "true" ? true : false;
          vm.isRobOrAppoint = from.query.isRobOrAppoint == "true" ? true : false;
          vm.source = from.query.source;

          vm.handleChangeOderInfo = from.query.handleChangeOderInfo;
          vm.handleComponentsTitle = from.query.handleComponentsTitle;
          vm.workTypeCode = from.query.workTypeCode;
          vm.localtionId = from.query.localtionId;
          vm.itemTypeCode = from.query.itemTypeCode;
        }
      }
      if (vm.workOrdeInfoDate && vm.workOrdeInfoDate[0]) {
        if (!vm.workOrdeInfoDate[0].designatePersonCode) {
          // 如果当前工单没有指派到人的的话，为一体机自动派工的工单，需要做选择人的处理  pc未派工挂单后完工同理
          vm.isHadDesP = true;
        }
      }
    });
  },
  beforeRouteLeave(to, from, next) {
    console.log('ggg',to.name)
    // 清除parameterObj 解决点击完工后在点挂单页面展示不正确bug
    // sessionStorage.removeItem("parameterObj");
    if (to.name != "Consumables") {
      // 不是去耗材列表则取消工单操作页面缓存
      this.$route.meta.keepAlive = false;
    }
    if (to.name == "OptionsTrouble") {
      this.$route.meta.keepAlive = true;
    }
    if (to.name == "CompletedInfo") {
      if (this.$refs.finished) {
        this.$refs.finished.$refs.imgs.emptyImg();
        this.$refs.finished.removeCache();
      }
    }
    if (to.name == "MyWorkbench" || to.name == "VieOderInfo") {
      //此处判断是如果返回上一层，你可以根据自己的业务更改此处的判断逻辑，酌情决定是否摧毁本层缓存。
      if (this.$vnode && this.$vnode.data.keepAlive) {
        if (this.$vnode.parent && this.$vnode.parent.componentInstance && this.$vnode.parent.componentInstance.cache) {
          if (this.$vnode.componentOptions) {
            var key =
              this.$vnode.key == null ? this.$vnode.componentOptions.Ctor.cid + (this.$vnode.componentOptions.tag ? `::${this.$vnode.componentOptions.tag}` : "") : this.$vnode.key;
            var cache = this.$vnode.parent.componentInstance.cache;
            var keys = this.$vnode.parent.componentInstance.keys;
            if (cache[key]) {
              if (keys.length) {
                var index = keys.indexOf(key);
                if (index > -1) {
                  keys.splice(index, 1);
                }
              }
              delete cache[key];
            }
          }
        }
      }
      this.$destroy();
    }
    // if (to.name == 'OptionsTrouble') {
    //   console.log(1212);  
    //   this.saveAutoConsumablesState();
    // }
    next();
  }
};
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped>
@import '~styles/mixins.styl';
@import '~styles/varibles.styl';

.container {
  font-size: calc(16px * var(--font-scale));
  height: 100%;
  overflow: auto;

  .item {
    .header {
      height: 0.57rem;
      display: flex;
      align-items: center;
      margin: 0.15rem 0 0.15rem 0.3rem;
      background: #fff;
      position: relative;

      .iconfont {
        font-size: 0.4rem;
        color: $btnColor;
      }

      .title-body {
        height: 0.6rem;
        display: flex;
        align-items: center;
        margin-left: 10px;
        background: #eceef8;
        padding: 0 0.24rem;
        border-radius: 0.3rem;
        white-space: nowrap;

        .dot {
          display: inline-block;
          width: 0.09rem;
          height: 0.09rem;
          background: $btnColor;
        }

        .title {
          font-size: 0.28rem;
          font-weight: 700;
          margin: 0 0.45rem 0 0.08rem;
        }

        .time {
          font-size: 0.3rem;
          color: #4F87FB;
        }
      }
    }
  }

  .vie-info {
    padding-top: 5px;

    .consumables-wrapper {
      background: #fff;
      overflow: hidden;

      .consumables-content {
        box-sizing: border-box;
        border-radius: 5px;
        overflow: hidden;
        margin: 0.32rem;
      }
    }

    .notice-wrapper {
      display: flex;
      height: auto;
      min-height: 0.98rem;
      line-height: 1.5em;
      padding: 0.32rem 0.45rem 0px 0.37rem;
      margin-left: 0rem;
      border-left: 1px solid #e5e5e5;

      .text-wrapper {
        color: $textColor;
        font-size: calc(16px * var(--font-scale));
      }
    }

    .designate-css {
      margin-top: -5px;
      padding-top: 10px;
    }
  }

  .disClick, .disabledClick {
    pointer-events: none;
  }

  .btns {
    height: 1.32rem;
    padding: 0.22rem 0.16rem;
    box-sizing: border-box;
    background-color: #fff;
    display: flex;
    justify-content: space-around;
    position: fixed;
    bottom: 0;
    width: 100%;
    z-index: 2;
    border-top: 1px solid $bgColor;

    .btn {
      margin: 0 0.16rem;
      height: 0.88rem;
      font-size: calc(15px * var(--font-scale));
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}

.line-css {
  line-height: 0.4rem !important;
  margin-bottom: 0.1rem;
}
/deep/ .van-picker .van-picker__toolbar > button {
  font-size: calc(16px * var(--font-scale))!important;
}
/deep/ .van-picker .van-ellipsis {
  font-size: calc(16px * var(--font-scale))!important;
}
</style>
