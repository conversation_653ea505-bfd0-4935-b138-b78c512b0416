<template>
  <div class="consumables-wrapper" v-if="isHaveConsumables">
    <div class="consumables-content">
      <div class="list" ref="lists" v-for="(item,index) of selectedConsumables" :key="index" >
        <div class="name">{{item.depThreeTypeName}}</div>
        <div class="count">
          <div class="coustom-number"> 
            <span class="iconfont less" @click="handleLessNumClick(index,item.depThreeTypeName)" v-if="showIcon">&#xe693;</span>
            <!-- <span class="num">{{item.num}}</span> -->
            <div class="number"> 
              <input   
              class="weui-input "
              type="text"
              maxlength="4"
              oninput="value=value.replace(/[^\d]/g,'')"
              v-model="item.num" placeholder="" />
            </div>
              <!-- onKeyUp ="if(value == '')value=1" -->
            <span class="iconfont add" @click="handleAddNumClick(index)" v-if="showIcon">&#xe691;</span>
          </div>

        </div>
      </div>
    </div>
  </div>
</template>

<script type=text/ecmascript-6>
  export default {
    name: "EchoConsumables",
    props:['selectedConsumables','showIcon','isAutoConsumables'],
    data () {
      return {
        isHaveConsumables:true
      }
    },
    methods: {
      /**
       * 点击减少耗材-
       * @param index
       */
      handleLessNumClick (index) {
        let num = this.selectedConsumables[index].num
        if (this.isAutoConsumables) {
          // 自动耗材模式：最小为0，不允许删除
          if (num > 0) {
            this.selectedConsumables[index].num = --num
          }
        } else {
          // 普通模式：最小为1
          if (num == 1 || num == '') {
            this.selectedConsumables.splice(index, 1)
          } else {
            this.selectedConsumables[index].num = --num
          }
        }
        
        if (this.selectedConsumables.length == 0) {
          this.isHaveConsumables = false
          this.$emit('selectTextColor')
        }
      },
      /**
       * 点击添加耗材+
       * @param index
       */
      handleAddNumClick (index) {
        let num = this.selectedConsumables[index].num
        this.selectedConsumables[index].num = ++num
      },
    },
    watch: {
      selectedConsumables (curCon) {
        if (curCon) {
          if (curCon.lenght !=0) {
            this.isHaveConsumables = true
          }else{
            this.isHaveConsumables = false
          }
        }
      }
    }
  }
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped>
  @import "~styles/mixins.styl"
  @import "~styles/varibles.styl"
  .coustom-number 
    display: flex;
    justify-content: flex-end;
    width: 55%;
    float: right;
  .number 
    width: 110px
    font-size: calc(15px * var(--font-scale))!important;
    color: #353535
    input 
      text-align: center 
  .consumables-wrapper
    marginBottom20()
    background: #fff
    overflow: hidden
    .consumables-content
      box-sizing: border-box
      border-radius: 5px
      overflow: hidden
      margin: .32rem
      .list
        display: flex
        justify-content: space-between
        itemBaseStyle()
        font-size: calc(16px * var(--font-scale))!important;
        background: #f5f6fb
        .name
          width: 3rem
          overflow: hidden
          text-overflow: ellipsis
          white-space: nowrap
        .count
          .less
            color: #d1d1d1
          .add
            color: $btnColor
          .num
            display: inline-block
            text-align: center
            width: 50px

    
  
</style>