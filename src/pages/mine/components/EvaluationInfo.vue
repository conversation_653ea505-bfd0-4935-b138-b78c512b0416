<template>
  <div class="finished-wrapper" :class="{ fold: fold }">
    <div class="pendOrder-desc">
      <div class="weui-cell item">
        <div class="weui-cell__hd">
          <label class="weui-label title">完工说明</label>
        </div>
      </div>
      <div class="weui-cell__bd border-rightbottom desc content-css">{{ finishedData.disFinishRemark }}</div>
    </div>
    <div v-if="workTypeCode != 3 && workTypeCode != 4 && workTypeCode != 15 && template != 3">
      <!--除了运输和订餐 自定义运送类-->
      <div class="weui-cell item border-rightbottom">
        <div class="weui-cell__hd actual-content">
          <label class="weui-label title">耗材实际使用</label>
          <span v-if="finishedData.actual.length == 0"></span>
        </div>
      </div>
      <div class="consumables-content content-css">
        <div class="list" v-for="(item, index) of finishedData.actual" :key="index">
          <div class="name">{{ item.depThreeTypeName }}</div>
          <div class="count">
            <span class="num">{{ item.num }}</span>
          </div>
        </div>
      </div>
    </div>
    <!-- 维修和保洁才有故障的相关处理 -->
    <div v-if="workTypeCode == 1 || workTypeCode == 2">
      <div v-for="(item, index) in finishedData.taskMalfunctionList" :key="index">
        <div class="weui-cell border-rightbottom item">
          <div class="weui-cell__hd">
            <label class="weui-label title">故障原因</label>
          </div>
          <div class="weui-cell__bd content-css row-css">{{ item.reasonName }}</div>
        </div>
        <div class="weui-cell border-rightbottom item" v-for="(name, i) in item.methodList" :key="i">
          <div class="weui-cell__hd">
            <label class="weui-label title">处理方法</label>
          </div>
          <div class="weui-cell__bd content-css row-css">{{ name.methodName }}</div>
        </div>
      </div>
    </div>
    <!-- 综合服务 自定义综合类不显示 -->
    <div class="weui-cell border-rightbottom item" v-if="(workTypeCode != 6 && workTypeCode != 15 && workTypeCode.length != 32) || template == 3">
      <div class="weui-cell__hd">
        <label class="weui-label title">总服务费</label>
      </div>
      <div class="weui-cell__bd content-css row-css">{{ finishedData.completePrice }}</div>
    </div>
    <div class="img-desc">
      <div class="text-title border-rightbottom title">
        <span class="img-desc-title">工单附件</span>
        <span v-if="!finishedData.disAttachmentUrl" class="img-desc-content"></span>
      </div>
      <div class="img-content">
        <div class="img-wrapper" v-for="ele of finishedData.disAttachmentUrl" :key="ele">
          <div class="img-box">
            <img
              v-preview="$YBS.imgUrlTranslation(ele)"
              :src="$YBS.imgUrlTranslation(ele)"
              class="img"
              preview-title-enable="true"
              preview-nav-enable="true"
              preview-top-title-tnable="true"
              preview-title-extend="false"
            />
          </div>
        </div>
      </div>
    </div>
    <div class="img-desc" v-if="finishedData.signUrl">
      <div class="text-title border-rightbottom title">
        <span class="img-desc-title">签字</span>
        <!-- <span v-if="!finishedData.disAttachmentUrl" class="img-desc-content"></span> -->
      </div>
      <div class="img-content">
        <div class="img-wrapper">
          <div class="img-box">
            <img
              v-preview="$YBS.imgUrlTranslation(finishedData.signUrl)"
              :src="$YBS.imgUrlTranslation(finishedData.signUrl)"
              class="img"
              preview-title-enable="true"
              preview-nav-enable="true"
              preview-top-title-tnable="true"
              preview-title-extend="false"
            />
          </div>
        </div>
      </div>
    </div>
    <div class="weui-cell item border-bottom item" v-if="finishedData.disDegree != '' || isHave">
      <div class="weui-cell__hd">
        <label class="weui-label title">满意度评价</label>
      </div>
      <div class="weui-cell__bd imgs row-css">
        <span>
          <img src="@/assets/images/on.png" alt v-for="(item, index) of onStarNum" :key="index" />
          <img src="@/assets/images/off.png" alt v-for="(item, index) of offStarNum" :key="5 - index" />
        </span>
        <span class="img-text content-css">{{ disDegreeText }}</span>
      </div>
    </div>
    <div class="weui-cell item border-bottom item" v-if="evaluationAdvice">
      <div class="weui-cell__hd">
        <label class="weui-label title">选择意见</label>
      </div>
      <div class="weui-cell__bd imgs row-css">
        <span class="img-text content-css">{{ evaluationAdvice }}</span>
      </div>
    </div>
    <div class="weui-cell item border-bottom item" v-if="finishedData.evaluationExplain">
      <div class="weui-cell__hd">
        <label class="weui-label title">描述</label>
      </div>
      <div class="weui-cell__bd imgs row-css">
        <span class="img-text content-css">{{ finishedData.evaluationExplain }}</span>
      </div>
    </div>
    <!-- <div class="border-bottom" v-if="finishedData.disDegree != '' || isHave">
      <div class="pendOrder-desc">
      <div class="weui-cell item">
        <div class="weui-cell__hd">
          <label class="weui-label title">评价说明</label>
        </div>
      </div>
      <div
        class="weui-cell__bd border-rightbottom desc content-css"
        >{{ finishedData.disFinishRemark }}</div>
      </div>
      <div class="weui-cell item">
        <div class="weui-cell__hd">
          <label class="weui-label title">满意度评价</label>
        </div>
        <div class="weui-cell__bd imgs row-css">
          <span>
            <img src="@/assets/images/on.png" alt v-for="(item,index) of onStarNum" :key="index" />
            <img src="@/assets/images/off.png" alt v-for="(item,index) of offStarNum" :key="5 -index" />
          </span>
          <span class="img-text content-css">{{disDegreeText}}</span>
        </div>
      </div>
    </div> -->
  </div>
</template>

<script>
export default {
  name: "EvaluationInfo",
  props: ["finishedData", "workTypeCode", "template"],
  data() {
    return {
      disDegreeText: "",
      onStarNum: 0,
      offStarNum: 0,
      isHave: false,
      configParams: [],
      evaluationAdvice: "",
    };
  },
  computed: {
    fold() {
      return this.finishedData.fold;
    },
  },
  mounted() {
    console.log(this.finishedData,'finishedData');
    if (this.finishedData.disDegree == "") {
      //        this.$parent.changeDisDegree()
      this.$emit("setDisDegreeToEmpty");
    }

    switch (this.finishedData.disDegree) {
      case "1":
        this.disDegreeText = "非常差";
        break;
      case "2":
        this.disDegreeText = "差";
        break;
      case "3":
        this.disDegreeText = "一般";
        break;
      case "4":
        this.disDegreeText = "满意";
        break;
      case "5":
        this.disDegreeText = "非常满意";
        break;
    }
    this.onStarNum = +this.finishedData.disDegree;
    this.offStarNum = 5 - this.finishedData.disDegree;
    if (this.finishedData.evaluationAdvice) {
      this.evaluationAdvice = this.finishedData.evaluationAdvice;
    }
  },
};
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped>
@import '~styles/mixins.styl';
@import '~styles/varibles.styl';

.finished-wrapper {
  timelineContent();

  .title-name {
    height: 0.55rem;
    display: flex;
    justify-content: center;
    align-items: center;

    .line {
      width: 2.5rem;
    }

    .title-content {
      position: absolute;
      padding: 0 7px;
      background: $bgColor;
      font-size: 14px;
      color: $textColor;
    }
  }

  .pendOrder-desc {
    background-color: #fff;

    .desc {
      line-height: 28px;
      padding: 5px 0.32rem 0;
      box-sizing: border-box;
      background-color: #fff;
      font-size: 0.32rem;
      text-indent: 2em;
      word-wrap: break-word;
      word-break: break-word;
    }
  }

  .imgs {
    text-align: right;

    img {
      width: 0.34rem;
      vertical-align: middle;
      margin-right: 5px;
    }

    .img-text {
      vertical-align: middle;
    }
  }

  .consumables-content {
    box-sizing: border-box;
    border-radius: 5px;
    overflow: hidden;
    background-color: #fff;

    .list {
      display: flex;
      justify-content: space-between;
      font-size: calc(15px * var(--font-scale))!important
      itemBaseStyle();
      padding: 0 1.2rem;

      .count {
        .num {
          display: inline-block;
          text-align: center;
          width: 50px;
        }
      }
    }
  }
}

.item {
  itemBaseStyle();

  .actual-content {
    display: flex;

    span {
      padding-left: 5px;
      color: $contentColor;
    }
  }

  .title {
    font-size: calc(16px * var(--font-scale))!important
    white-space: nowrap;
  }
}

.img-desc {
  .text-title {
    line-height: 1rem;
    padding-left: 0.32rem;

    .img-desc-title {
      display: inline-block;
      width: 105px;
    }

    .img-desc-content {
      color: $contentColor;
    }
  }

  .img-content {
    background: #fff;
    padding: 0 0.3rem;
    display: flex;

    padding: 0 0.3rem;
    overflow-x: auto;
    display: -webkit-box;
    display: -ms-flexbox;
    margin-bottom: 0.2rem;
      .img-wrapper {
        width: 30%;
        height: 1.4rem;
        margin: 0.1rem;
        position: relative;

        .img-box {
          height: 100%;
          overflow: hidden;

          .img {
            width: 100%;
          }
        }
      }
  }
}

.name-img {
  background-color: #fff;
  width: 100%;
  padding: 10px;
  box-sizing: border-box;

  img {
    width: 100%;
  }
}

.content-css {
  color: $contentColor;
  font-size: calc(16px * var(--font-scale))!important
}

.row-css {
  line-height: 1.5em;
}

.fold {
  display: none;
}
</style>
