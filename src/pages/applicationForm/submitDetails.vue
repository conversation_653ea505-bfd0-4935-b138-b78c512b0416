<template>
  <div class="inner">
    <Header title="领用申请单" :showLeftBtn="false"></Header>
    <div class="exactness" v-if="$route.query.code == 200">
      <div class="box">
        <div class="wrapper"></div>
      </div>
      <div style="text-align: center">提交成功</div>
      <div v-if="$route.query.multipleApplications" class="multiple-apps-notice">
        已成功创建 {{ $route.query.multipleApplications }} 张申请单（按仓库分组）
      </div>
      <div style="width: 100%">
        <div v-for="item in list" :key="item.id" class="listStyle">
          <div class="txt">
            <div class="txt_l">耗材名称</div>
            <div class="txt_r">{{ item.materialName }}</div>
          </div>
          <div class="txt">
            <div class="txt_l">耗材编码</div>
            <div class="txt_r">{{ item.materialCode }}</div>
          </div>
          <div class="txt">
            <div class="txt_l">耗材类型</div>
            <div class="txt_r">{{ item.materialTypeName }}</div>
          </div>
          <div class="txt">
            <div class="txt_l">规格型号</div>
            <div class="txt_r">{{ item.model }}</div>
          </div>
          <div class="txt">
            <div class="txt_l">数量</div>
            <div class="txt_r">{{ item.changeNum }}</div>
          </div>
        </div>
      </div>
      <div style="height: 50px"></div>
      <div class="btn">
         <van-button style="width: 90%" color="#3562db" v-if="getworkProcessing" @click="workProcessing">返回工单</van-button>
        <van-button style="width: 90%" color="#3562db" v-else @click="returnHome">返回首页</van-button>
      </div>
    </div>
    <div class="wrong" v-else>
      <div>
        <div class="wrapper"></div>
      </div>
      <div style="font-size: 17px">提交失败</div>
      <div class="message">{{ message }}</div>
      <div class="btn2">
        <van-button style="width: 90%" color="#3562db" @click="goBack">返回上一页</van-button>
      </div>
    </div>
  </div>
</template>

<script>
  import moment from "moment";
  import fontSizeMixin from "@/mixins/fontSizeMixin";
  import {
    ImagePreview
  } from "vant";
  export default {
    mixins: [fontSizeMixin],
    data() {
      return {
        moment,
        list: [],
        message: "",
        getworkProcessing:""
      };
    },
    created() {
      if (this.$route.query.code == 200) {
        this.list = this.$route.query.data || [];
      } else {
        this.message = this.$route.query.message;
      }
      if(localStorage.getItem('workProcessing')){
        this.getworkProcessing = localStorage.getItem('workProcessing')
      } else {
        this.getworkProcessing = ''
      }
    },
    mounted() {
    },
    methods: {
      //返回工单详情
      workProcessing() {
        this.$router.push({
          path: "/Completed",
          query: {
            pageSouce: 'workProcessing'
          }
        });
        // localStorage.removeItem('workProcessing');
      },
      previewImage(url) {
        if (!url) return;
        ImagePreview({
          images: [this.$YBS.imgUrlTranslation(url)],
          closeable: true
        });
      },
      goBack() {
        this.$router.go(-1);
      },
      returnHome() {
        this.$YBS.apiCloudCloseFrame();
      }
    }
  };

</script>

<style scoped lang="scss">
  .inner {
    width: 100%;
    height: 100%;
    background-color: #fff;

    .exactness {
      width: 100%;
      font-size: calc(14px * var(--font-scale))!important;

      .listStyle {
        width: 95%;
        background-color: #f6f8fa;
        border-radius: 10px;
        margin: 10px auto;
        padding: 5px 0;

        .txt {
          display: flex;
          padding: 7px 10px;

          .alarmLevel0 {
            color: #00b42a;
          }

          .alarmLevel1 {
            color: #3562db;
          }

          .alarmLevel2 {
            color: #ff7d00;
          }

          .alarmLevel3 {
            color: #f53f3f;
          }

          .txt_l {
            margin: auto 0;
            width: 100px;
            color: #4e5969;
            font-weight: 300;
          }

          .txt_r {
            color: #1d2129;
            flex: 1;
          }
        }

        .txtOpertaion {
          display: flex;
          height: 35px;
          line-height: 35px;
          padding: 0px 10px;

          .txt_l {
            width: 90px;
            color: #4e5969;
            font-weight: 300;
          }

          .btn {
            flex: 1;
            text-align: right;

            span {
              display: inline-block;
              height: 30px;
              line-height: 30px;
              padding: 0px 15px;
              background-color: #3562db;
              color: #fff;
            }
          }
        }
      }

      .box {
        display: flex;
        justify-content: center;

        .wrapper {
          margin: 10px 0;
          width: 40px;
          height: 40px;
          background: url("../../assets/images/icon-wrapper.png");
          background-size: 100% auto;
        }
      }
    }

    .wrong {
      width: 100%;

      >div {
        display: flex;
        justify-content: center;

        .wrapper {
          margin: 10px 0;
          width: 40px;
          height: 40px;
          background: url("../../assets/images/icon-wrong.png");
          background-size: 100% auto;
        }
      }

      .message {
        margin: 15px 0;
        color: #4e5969;
        padding: 0 15px;
        line-height: 20px;
      }
    }
  }

  .btn {
    width: 100%;
    display: flex;
    background-color: #fff;
    justify-content: space-around;
    position: fixed;
    bottom: 0;
    padding-bottom: 15px;
    /deep/ .van-button--normal {
      font-size: calc(15px * var(--font-scale))!important;
    }
  }

  .btn2 {
    width: 100%;
    display: flex;
    justify-content: space-around;
  }

  .material-img {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: 4px;
  }
  
  .multiple-apps-notice {
    padding: 8px 15px;
    margin: 10px auto;
    width: 90%;
    text-align: center;
    background-color: #E8F3FF;
    color: #3562DB;
    border-radius: 4px;
    font-size: calc(14px * var(--font-scale))!important;
  }

</style>
