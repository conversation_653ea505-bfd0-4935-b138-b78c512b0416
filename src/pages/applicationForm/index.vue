<template>
  <div class="inner" :class="{'large-font': isLargeFont}">
    <Header title="物料申请单" @backFun="goBack" @taskScanCode="goToMyInOut">
       <!-- <span style="font-size: 14px;color: #3562db;">我的出入库</span> -->
    </Header>
    <div>
      <van-field readonly required right-icon="arrow" v-model="outboundTypeName" label="申请类型" placeholder="请选择申请类型"
        @click="openPolice" />
      <van-popup v-model="showOutbound" position="bottom">
        <van-picker value-key="name" show-toolbar :columns="outboundTypeList" @confirm="onPolice"
          @cancel="showOutbound = false" />
      </van-popup>
      <van-field 
        :right-icon="isFromWorkOrder ? '' : 'plus'" 
        :readonly="isFromWorkOrder"
        color="blue" 
        v-model="workOrderNumber" 
        label="关联工单" 
        placeholder="请输入或选择工单号"
        @click-right-icon="associatedWorkOrder" />
      <van-field v-if="showRelatedExportField" v-model="relatedExport" label="关联出库单" placeholder="请输入或选择关联出库单"
        right-icon="plus" @click-right-icon="openRelatedExport" />
      <van-popup v-model="showRelatedExport" position="bottom">
        <van-picker value-key="name" show-toolbar :columns="relatedExportList" @confirm="onRelatedExport"
          @cancel="showRelatedExport = false" />
      </van-popup>

    </div>
    <div style="margin-top: 10px">
      <van-field readonly required right-icon="arrow" label="领用物料" placeholder="请选择" @click="selectMaterial" />
      <div class="box" v-if="materialData.length > 0">
        <div v-for="(item, index) in materialData" :key="index" class="listStyle">
          <p>{{ item.materialName }}</p>
          <div>
            <van-stepper 
              v-model="item.changeNum" 
              :min="1" 
              :max="item.useNum ? Number(item.useNum) : undefined" 
            />
            <van-icon name="delete" size="20px" color="#F53F3F" @click="deletePersonnelMaterial(item.id)" />
          </div>
        </div>
      </div>
    </div>
    <div style="height: 50px"></div>
    <div class="btn">
      <van-button style="width: 90%" color="#3562db" @click="confirm">确定</van-button>
    </div>
  </div>
</template>

<script>
  import Vue from "vue";
  import fontSizeMixin from "@/mixins/fontSizeMixin";
  import {
    Stepper
  } from "vant";
  import {
    mapState
  } from "vuex";
  import moment from "moment";

  Vue.use(Stepper);
  export default {
    mixins: [fontSizeMixin],
    data() {
      return {
        moment,
        outboundTypeName: "",
        outboundTypeId: "",
        workOrderNumber: "",
        materialData: [],
        dataList: [],
        showOutbound: false,
        outboundTypeList: [
          {
            name: "物料申领",
            id: 1
          },
          {
            name: "物料退还",
            id: 2
          }
        ],
        isFromWorkOrder: false,
        relatedExport: "",
        showRelatedExport: false,
        relatedExportList: [],
      };
    },
    computed: {
      ...mapState(["loginInfo"]),

      showRelatedExportField() {
        return this.outboundTypeId == 2;
      },
      isLargeFont() {
        return localStorage.getItem('fontSizePreference') === 'x-large' || localStorage.getItem('fontSizePreference') === 'large';
      }
    },
    watch: {
      $route(to, from) {
        console.log(to,'this.$route.querythis.$route.querythis.$route.query');
        this.materialData=[]
        if (from.name == "MyWorkbench") {
          this.workOrderNumber = this.$route.query.workOrderNumber || "";
        } else if (from.name == "selectMaterial") {
          this.materialData = this.$store.state.materialData || [];
        }
        if (from.name == "CompletedInfo") {
           localStorage.setItem('workProcessing', 'workProcessing');
          this.outboundTypeName = this.$route.query.outboundName || "",
            this.outboundTypeId = this.$route.query.outboundType || "",
            this.workOrderNumber = this.$route.query.workNum || ""
            this.isFromWorkOrder = !!this.$route.query.workNum;
        }
        
        // 从关联出库单选择页面返回
        if (from.name == "relatedExport" && this.$route.query.relatedExport) {
          this.relatedExport = this.$route.query.relatedExport;
        }

      },
      workOrderNumber: {
        handler(newVal) {
          if (newVal) {  // 只有当 workOrderNumber 有值时才调用
            this.getOutDepotNumberByWorkNum();
          }
        }
      },
      // $route: {
      //   immediate: true,
      //   handler(newRoute) {
      //     console.log(newRoute,'newRoutenewRoutenewRoutenewRoutenewRoute');

      //   }
      // }
    },
    created() {
      this.init();
      // 检查是否从工单详情页面跳转过来
      this.isFromWorkOrder = !!this.$route.query.workNum;
    },
    mounted() {
        localStorage.setItem('workProcessing', 'workProcessing');
        this.outboundTypeName = this.$route.query.outboundName || "";
        this.outboundTypeId = this.$route.query.outboundType || "";
        this.workOrderNumber = this.$route.query.workNum || "";
        // 检查是否从工单详情页面跳转过来
        this.isFromWorkOrder = !!this.$route.query.workNum;
    },
    activated() {
      this.$YBS.apiCloudEventKeyBack(this.goBack);
    },
    methods: {
      goToMyInOut() {
        this.$router.push({
          path: '/myAccessRequest',
          query: {
            pageSouce: 'h5'
          }
        });
      },
      getOutDepotNumberByWorkNum() {
        this.$api.getOutDepotNumberByWorkNum({
          workNum: this.workOrderNumber
        }).then(res => {
          console.log('根据工单号查询出库单号', res)
          this.relatedExportList = res;
          if(res.length > 0 && !this.relatedExport) {
            this.relatedExport = res[0];
          }
        });
      },
      onRelatedExport(value) {
        this.relatedExport = value;
        this.showRelatedExport = false;
      },
      openRelatedExport() {
        this.$router.push({
          path: "/applicationForm/relatedExport",
          query: {
            workNum: this.workOrderNumber,
            outboundType: this.outboundTypeId,
            outboundName: this.outboundTypeName
          }
        });
      },



      // 初始化
      init() {
        // 初始化逻辑已简化，不再需要获取部门和人员信息
      },
      confirm() {
        if (this.outboundTypeName == "") return this.$toast.fail("请选择申请类型");
        if (this.materialData.length == 0) return this.$toast.fail("请选择领用物料");

        // 按仓库ID分组物料
        const materialsByWarehouse = {};
        this.materialData.forEach(item => {
          if (!materialsByWarehouse[item.warehouseId]) {
            materialsByWarehouse[item.warehouseId] = {
              warehouseId: item.warehouseId,
              warehouseName: item.warehouseName,
              materials: []
            };
          }
          materialsByWarehouse[item.warehouseId].materials.push(item);
        });

        // 获取仓库数量
        const warehouseCount = Object.keys(materialsByWarehouse).length;
        
        // 如果有多个仓库，提示用户将创建多个申请单
        if (warehouseCount > 1) {
          this.$dialog.confirm({
            title: '提示',
            message: `您选择的物料来自${warehouseCount}个不同仓库，将创建${warehouseCount}个申请单，是否继续？`,
          }).then(() => {
            this.submitMultipleApplications(materialsByWarehouse);
          }).catch(() => {
            // 取消操作
          });
        } else {
          // 只有一个仓库，直接提交
          this.submitMultipleApplications(materialsByWarehouse);
        }
      },

      // 提交多个申请单
      submitMultipleApplications(materialsByWarehouse) {
        // 计算总的申请单数量
        const totalCount = Object.keys(materialsByWarehouse).length;
        let processedCount = 0;
        let hasError = false;
        let errorMessage = "";
        
        // 依次处理每个仓库的申请
        Object.values(materialsByWarehouse).forEach(warehouse => {
          // 构建基础参数
          let params = {
            userId: this.loginInfo.staffId,
            userName: this.loginInfo.staffName,
            status: '2',
            createSource: '1',
            workNum: this.workOrderNumber,
            applicantId: this.loginInfo.staffId,
            applicantName: this.loginInfo.staffName,
            applicantDepartmentId: this.loginInfo.deptId,
            applicantDepartmentName: this.loginInfo.deptName,
            warehouseId: warehouse.warehouseId,
            warehouseName: warehouse.warehouseName,
            materialRecordArrayStr: JSON.stringify(warehouse.materials.map(item => {
              // 计算单个物料金额并保留两位小数，避免精度问题
              const unitPrice = Number(item.unitPrice || 0);
              const quantity = Number(item.changeNum);
              const amount = parseFloat((unitPrice * quantity).toFixed(2));
              
              return {
                ...item,
                operateCount: item.changeNum,
                sumAmount: amount
              };
            }))
          };

          // 计算当前仓库所有物料的总金额，保留两位小数
          const totalAmount = parseFloat(warehouse.materials.reduce((sum, item) => {
            const unitPrice = Number(item.unitPrice || 0);
            const quantity = Number(item.changeNum);
            return sum + parseFloat((unitPrice * quantity).toFixed(2));
          }, 0).toFixed(2));

          // 如果是物料退还，添加关联出库单信息和入库总金额
          if(this.outboundTypeId == 2) {
            params.outwarehouseRecordNumber = this.relatedExport;
            params.inwarehouseAmount = totalAmount;
          } else if(this.outboundTypeId == 1) {
            // 物料申领，添加出库总金额
            params.outwarehouseAmount = totalAmount;
          }

          // 根据申请类型调用不同的接口
          let apiCall;
          if (this.outboundTypeId == 1) {
            // 物料申领调用submitOutbound接口
            apiCall = this.$api.submitOutbound(params);
          } else if (this.outboundTypeId == 2) {
            // 物料退换调用submitInbound接口
            apiCall = this.$api.submitInbound(params);
          } else {
            hasError = true;
            errorMessage = "不支持的申请类型";
            return;
          }

          // 执行API调用
          apiCall
            .then(() => {
              processedCount++;
              if (processedCount === totalCount) {
                // 所有申请单都已处理完成
                this.$router.push({
                  path: "/submitDetails",
                  query: {
                    data: this.materialData,
                    workNum: this.workOrderNumber,
                    code: 200,
                    multipleApplications: totalCount > 1 ? totalCount : undefined
                  }
                });
              }
            })
            .catch(err => {
              hasError = true;
              processedCount++;
              errorMessage = (err.data && err.data.message) ? err.data.message : "提交失败";
              
              if (processedCount === totalCount) {
                // 所有申请单都已处理，但有错误发生
                this.$router.push({
                  path: "/submitDetails",
                  query: {
                    message: errorMessage,
                    code: (err.data && err.data.code) ? err.data.code : "500"
                  }
                });
              }
            });
        });
      },
      deletePersonnelMaterial(id) {
        this.materialData = this.materialData.filter(item => item.id !== id);
      },
      onPolice(value) {
        this.outboundTypeName = value.name;
        this.outboundTypeId = value.id;
        this.showOutbound = false;
      },
      openPolice() {
        this.showOutbound = true;
      },
      goBack() {
        if (localStorage.workProcessing) {
          localStorage.removeItem('workProcessing');
          this.$router.go(-1);
        } else {
          this.$YBS.apiCloudCloseFrame();
        }


      },
      associatedWorkOrder() {
        this.$router.push({
          path: "/workbench",
          query: {
            type: 3,
            workTypeCode: "",
            leaguerType: 1,
            outbound: 1
          }
        });
      },
      selectMaterial() {
        const query = {
          materialData: this.materialData
        };
        
        // 只有当申请类型为库存增加(id=1)或出库归还(id=2)时，才传递 depotNumber 参数
        if (this.outboundTypeId == 2) {
          query.depotNumber = this.relatedExport || "";
        }
        
        this.$router.push({
          path: "/selectMaterial",
          query
        });
      }
    }
  };

</script>

<style scoped lang="scss">
  .inner {
    width: 100%;
    height: 100%;
    background-color: #f2f4f9;
  }

  /deep/ .van-icon-plus:before {
    content: "\E6FA";
    color: #3563dc;
  }

  .box {
    padding: 15px 10px 10px 10px;
    background-color: #fff;

    .listStyle {
      padding: 5px 10px;
      height: 35px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      background-color: #f7f8fa;
      border-radius: 5px;
      margin-bottom: 10px;

      >p {
        flex: 3;
        line-height: 25px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      >div {
        flex: 2;
        line-height: 25px;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
    }
  }

  .btn {
    width: 100%;
    background-color: #fff;
    display: flex;
    justify-content: space-around;
    position: fixed;
    bottom: 0;
    padding-bottom: 15px;
  }

  /deep/ .van-tabs__line {
    background-color: #3562db;
  }
  /deep/ .van-field .van-field__label span {
    font-size: calc(16px * var(--font-scale))!important;
  }
  /deep/ .van-field .van-field__control {
    font-size: calc(14px * var(--font-scale))!important;
  }
  .large-font  /deep/ .van-field .van-field__label {
    width: 7.2em!important;
  }
  /deep/ .van-picker .van-picker__toolbar >button,
  /deep/ .van-picker .van-picker-column__item {
    font-size: calc(16px * var(--font-scale))!important;
  }
  /deep/ .van-cascader .van-cascader__header .van-cascader__title,
  /deep/ .van-cascader .van-tab__text,
  /deep/ .van-cascader .van-cascader__option {
    font-size: calc(16px * var(--font-scale))!important;
  }
  /deep/ .listStyle > p {
  font-size: calc(14px * var(--font-scale))!important;
}
/deep/ .van-stepper__input {
  font-size: calc(14px * var(--font-scale))!important;
}
/deep/ .van-button--normal {
  font-size: calc(15px * var(--font-scale))!important;
}
</style>
